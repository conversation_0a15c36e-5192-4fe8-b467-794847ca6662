<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Add New Partner</h1>
          <p class="text-gray-600 mt-1">Create a new partner with settings and services</p>
        </div>
        <button
          @click="goBack"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <ArrowLeftIcon class="w-4 h-4 mr-2" />
          Back to Partners
        </button>
      </div>
    </div>

    <!-- Partner Creation Wizard -->
    <Wizard
      :steps="wizardSteps"
      :loading="loading"
      :can-proceed="canProceedToNext"
      :can-finish="canFinishWizard"
      finish-button-text="Create Partner"
      @step-change="handleStepChange"
      @next="handleNext"
      @previous="handlePrevious"
      @finish="handleFinish"
    >
      <!-- Step 1: Partner Information -->
      <template #step-partner-info>
        <div class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Partner Name *</label>
              <input
                v-model="partnerForm.name"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter partner name"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Email *</label>
              <input
                v-model="partnerForm.email"
                type="email"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter email address"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
              <input
                v-model="partnerForm.phone"
                type="text"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter phone number"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Partner Type</label>
              <select
                v-model="partnerForm.partner_type"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="betting">Betting</option>
                <option value="payment">Payment</option>
                <option value="technology">Technology</option>
                <option value="marketing">Marketing</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                v-model="partnerForm.status"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="pending">Pending</option>
                <option value="suspended">Suspended</option>
              </select>
            </div>
          </div>
        </div>
      </template>

      <!-- Step 2: Partner Settings -->
      <template #step-partner-settings>
        <div class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">API Key *</label>
              <input
                v-model="settingsForm.api_key"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm font-mono"
                placeholder="Enter API key"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">IP Address</label>
              <input
                v-model="settingsForm.ip_address"
                type="text"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter IP address"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Callback URL</label>
              <input
                v-model="settingsForm.callback_url"
                type="url"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="https://example.com/callback"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Currency</label>
              <select
                v-model="settingsForm.currency"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="KES">KES - Kenyan Shilling</option>
                <option value="USD">USD - US Dollar</option>
                <option value="EUR">EUR - Euro</option>
                <option value="GBP">GBP - British Pound</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Denomination</label>
              <input
                v-model="settingsForm.denomination"
                type="text"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter denomination"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Timezone</label>
              <select
                v-model="settingsForm.timezone"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="Africa/Nairobi">Africa/Nairobi</option>
                <option value="UTC">UTC</option>
                <option value="America/New_York">America/New_York</option>
                <option value="Europe/London">Europe/London</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Billing Mode</label>
              <select
                v-model="settingsForm.billing_mode"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="prepay">Prepay</option>
                <option value="postpay">Postpay</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Rate Limit (per minute)</label>
              <input
                v-model.number="settingsForm.rate_limit"
                type="number"
                min="1"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="60"
              />
            </div>
          </div>

          <!-- Websites -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Websites</label>
            <div class="space-y-2">
              <div
                v-for="(website, index) in settingsForm.websites"
                :key="index"
                class="flex items-center space-x-2"
              >
                <input
                  v-model="settingsForm.websites[index]"
                  type="url"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="https://example.com"
                />
                <button
                  v-if="settingsForm.websites.length > 1"
                  @click="removeWebsite(index)"
                  type="button"
                  class="inline-flex items-center px-2 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  ×
                </button>
              </div>
              <button
                @click="addWebsite"
                type="button"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                + Add Website
              </button>
            </div>
          </div>
        </div>
      </template>

      <!-- Step 3: Services -->
      <template #step-services>
        <div class="space-y-6">
          <div v-if="availableServices.length > 0">
            <h4 class="text-md font-medium text-gray-900 mb-4">Select Services</h4>
            <div class="space-y-3">
              <div
                v-for="service in availableServices"
                :key="service.id"
                class="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
              >
                <div class="flex items-center">
                  <input
                    :id="`service-${service.id}`"
                    v-model="selectedServices"
                    :value="service.id"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label :for="`service-${service.id}`" class="ml-3">
                    <div class="text-sm font-medium text-gray-900">{{ service.name }}</div>
                  </label>
                </div>
                <div v-if="selectedServices.includes(service.id)" class="flex items-center space-x-2">
                  <label class="text-xs text-gray-500">Rate Limit:</label>
                  <input
                    v-model.number="serviceSettings[service.id].rate_limit_per_minute"
                    type="number"
                    min="1"
                    class="w-20 px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="60"
                  />
                  <span class="text-xs text-gray-500">/min</span>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No services available</h3>
            <p class="mt-1 text-sm text-gray-500">Services will be loaded automatically.</p>
          </div>
        </div>
      </template>
    </Wizard>
  </div>
</template>


<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeftIcon } from '@heroicons/vue/24/outline'
import Wizard, { type WizardStep } from '@/components/Wizard.vue'
import { partnerApi, type Service } from '@/services/partnerApi'
import { servicesApi } from '@/services/servicesApi'

// Router
const router = useRouter()

// Reactive data
const loading = ref(false)
const currentStep = ref(0)
const availableServices = ref<Service[]>([])
const selectedServices = ref<number[]>([])
const serviceSettings = reactive<Record<number, { rate_limit_per_minute: number }>>({})

// Wizard steps
const wizardSteps: WizardStep[] = [
  {
    id: 'partner-info',
    title: 'Partner Information',
    description: 'Basic partner details and contact information'
  },
  {
    id: 'partner-settings',
    title: 'Partner Settings',
    description: 'API configuration and technical settings'
  },
  {
    id: 'services',
    title: 'Services',
    description: 'Select and configure partner services'
  }
]

// Form data
const partnerForm = reactive({
  name: '',
  email: '',
  phone: '',
  partner_type: 'betting',
  status: 'active'
})

const settingsForm = reactive({
  api_key: '',
  ip_address: '',
  callback_url: '',
  currency: 'KES',
  denomination: '',
  timezone: 'Africa/Nairobi',
  billing_mode: 'prepay' as 'prepay' | 'postpay',
  rate_limit: 60,
  websites: ['']
})

// Computed properties
const canProceedToNext = computed(() => {
  switch (currentStep.value) {
    case 0: // Partner Info
      return partnerForm.name.trim() !== '' && partnerForm.email.trim() !== ''
    case 1: // Settings
      return settingsForm.api_key.trim() !== ''
    case 2: // Services
      return true // Services are optional
    default:
      return true
  }
})

const canFinishWizard = computed(() => {
  return canProceedToNext.value
})

// Methods
const handleStepChange = (stepIndex: number) => {
  currentStep.value = stepIndex
}

const handleNext = () => {
  // Validation can be added here
}

const handlePrevious = () => {
  // Any cleanup can be added here
}

const handleFinish = async () => {
  loading.value = true
  try {
    // Create partner
    // This would need a create partner API endpoint
    console.log('Creating partner with data:', {
      partner: partnerForm,
      settings: settingsForm,
      services: selectedServices.value.map((serviceId: number) => ({
        service_id: serviceId,
        ...serviceSettings[serviceId]
      }))
    })

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Navigate back to partners list
    router.push({ name: 'partners' })
  } catch (error) {
    console.error('Error creating partner:', error)
  } finally {
    loading.value = false
  }
}

const addWebsite = () => {
  settingsForm.websites.push('')
}

const removeWebsite = (index: number) => {
  if (settingsForm.websites.length > 1) {
    settingsForm.websites.splice(index, 1)
  }
}

const goBack = () => {
  router.push({ name: 'partners' })
}

// Watch for selected services changes to initialize settings
watch(selectedServices, (newServices, oldServices) => {
  // Add settings for newly selected services
  newServices.forEach(serviceId => {
    if (!serviceSettings[serviceId]) {
      serviceSettings[serviceId] = { rate_limit_per_minute: 60 }
    }
  })

  // Remove settings for deselected services
  oldServices?.forEach(serviceId => {
    if (!newServices.includes(serviceId)) {
      delete serviceSettings[serviceId]
    }
  })
}, { deep: true })

// Load available services
const loadServices = async () => {
  try {
    const response = await servicesApi.getServices({ limit: 100 })
    if (response.status === 200) {
      availableServices.value = response.message.data || []
    }
  } catch (error) {
    console.error('Error loading services:', error)
  }
}

// Lifecycle
onMounted(() => {
  loadServices()
})
</script>
                      type="checkbox"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label :for="`permission-${permission.id}`" class="ml-2 text-sm text-gray-700 flex-1">
                      {{ permission.name }}
                    </label>
                    <span v-if="permission.description" class="text-xs text-gray-400 ml-2">
                      {{ permission.description }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="goBack"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {{ loading ? 'Creating...' : 'Create Role' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeftIcon } from '@heroicons/vue/24/outline'
import { systemApi, type Permission } from '@/services/systemApi'
import { ROLES } from '@/config/permissions'

// Router
const router = useRouter()

// Reactive data
const loading = ref(false)
const permissions = ref<Permission[]>([])

// Role form
const roleForm = ref({
  role_name: '',
  description: '',
  permissions: [] as number[],
  status: 1
})

// Role templates from config
const roleTemplates = Object.values(ROLES).map(role => ({
  id: role.id,
  name: role.name,
  description: role.description,
  permissions: role.permissions
}))

// Computed properties
const groupedPermissions = computed(() => {
  const grouped: Record<string, Permission[]> = {}
  
  permissions.value.forEach(permission => {
    const module = permission.module || 'general'
    if (!grouped[module]) {
      grouped[module] = []
    }
    grouped[module].push(permission)
  })
  
  return grouped
})

// Methods
const fetchPermissions = async () => {
  try {
    const response = await systemApi.getPermissions({ limit: 1000 })
    if (response.status === 200) {
      permissions.value = response.message?.data || []
    }
  } catch (error) {
    console.error('Error fetching permissions:', error)
  }
}

const applyTemplate = (template: typeof roleTemplates[0]) => {
  roleForm.value.role_name = template.name
  roleForm.value.description = template.description
  roleForm.value.permissions = [...template.permissions]
}

const isModuleSelected = (modulePermissions: Permission[]) => {
  return modulePermissions.every(permission => 
    roleForm.value.permissions.includes(permission.id)
  )
}

const toggleModule = (modulePermissions: Permission[]) => {
  const allSelected = isModuleSelected(modulePermissions)
  
  if (allSelected) {
    // Remove all permissions from this module
    modulePermissions.forEach(permission => {
      const index = roleForm.value.permissions.indexOf(permission.id)
      if (index > -1) {
        roleForm.value.permissions.splice(index, 1)
      }
    })
  } else {
    // Add all permissions from this module
    modulePermissions.forEach(permission => {
      if (!roleForm.value.permissions.includes(permission.id)) {
        roleForm.value.permissions.push(permission.id)
      }
    })
  }
}

const selectAllPermissions = () => {
  roleForm.value.permissions = permissions.value.map(p => p.id)
}

const clearAllPermissions = () => {
  roleForm.value.permissions = []
}

const saveRole = async () => {
  loading.value = true
  try {
    const response = await systemApi.createRole({
      role_name: roleForm.value.role_name,
      description: roleForm.value.description,
      permissions: roleForm.value.permissions
    })
    
    if (response.status === 200) {
      router.push({ name: 'system-roles' })
    } else {
      console.error('Failed to create role:', response.message)
    }
  } catch (error) {
    console.error('Error creating role:', error)
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push({ name: 'system-roles' })
}

const formatModuleName = (module: string) => {
  return module.charAt(0).toUpperCase() + module.slice(1).replace(/[_-]/g, ' ')
}

// Initialize data
onMounted(() => {
  fetchPermissions()
})
</script>
