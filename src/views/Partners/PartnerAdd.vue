<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Add New Role</h1>
          <p class="text-gray-600 mt-1">Create a new system role with specific permissions</p>
        </div>
        <button
          @click="goBack"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <ArrowLeftIcon class="w-4 h-4 mr-2" />
          Back to Roles
        </button>
      </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <form @submit.prevent="saveRole" class="space-y-6">
        <!-- Basic Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Role Name *</label>
              <input
                v-model="roleForm.role_name"
                type="text"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter role name"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                v-model="roleForm.status"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option :value="1">Active</option>
                <option :value="0">Inactive</option>
              </select>
            </div>
          </div>
          
          <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              v-model="roleForm.description"
              rows="3"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Describe the role and its purpose"
            ></textarea>
          </div>
        </div>

        <!-- Role Templates -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Role Templates</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div 
              v-for="template in roleTemplates" 
              :key="template.id"
              @click="applyTemplate(template)"
              class="border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200"
            >
              <h4 class="font-medium text-gray-900">{{ template.name }}</h4>
              <p class="text-sm text-gray-500 mt-1">{{ template.description }}</p>
              <div class="mt-2">
                <span class="text-xs text-blue-600">{{ template.permissions.length }} permissions</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Permissions -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Permissions</h3>
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center justify-between mb-4">
              <span class="text-sm font-medium text-gray-700">
                {{ roleForm.permissions.length }} permissions selected
              </span>
              <div class="flex space-x-2">
                <button
                  type="button"
                  @click="selectAllPermissions"
                  class="text-sm text-blue-600 hover:text-blue-800"
                >
                  Select All
                </button>
                <button
                  type="button"
                  @click="clearAllPermissions"
                  class="text-sm text-gray-600 hover:text-gray-800"
                >
                  Clear All
                </button>
              </div>
            </div>
            
            <div class="max-h-80 overflow-y-auto space-y-4">
              <div v-for="(modulePermissions, module) in groupedPermissions" :key="module" class="border border-gray-200 rounded-lg">
                <div class="px-3 py-2 bg-white border-b border-gray-200 rounded-t-lg">
                  <div class="flex items-center">
                    <input
                      :id="`module-${module}`"
                      type="checkbox"
                      :checked="isModuleSelected(modulePermissions)"
                      @change="toggleModule(modulePermissions)"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label :for="`module-${module}`" class="ml-2 text-sm font-medium text-gray-900 capitalize">
                      {{ formatModuleName(module) }}
                    </label>
                    <span class="ml-auto text-xs text-gray-500">
                      {{ modulePermissions.length }} permissions
                    </span>
                  </div>
                </div>
                <div class="px-3 py-2 space-y-2">
                  <div v-for="permission in modulePermissions" :key="permission.id" class="flex items-center">
                    <input
                      :id="`permission-${permission.id}`"
                      v-model="roleForm.permissions"
                      :value="permission.id"
                      type="checkbox"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label :for="`permission-${permission.id}`" class="ml-2 text-sm text-gray-700 flex-1">
                      {{ permission.name }}
                    </label>
                    <span v-if="permission.description" class="text-xs text-gray-400 ml-2">
                      {{ permission.description }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            @click="goBack"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {{ loading ? 'Creating...' : 'Create Role' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeftIcon } from '@heroicons/vue/24/outline'
import { systemApi, type Permission } from '@/services/systemApi'
import { ROLES } from '@/config/permissions'

// Router
const router = useRouter()

// Reactive data
const loading = ref(false)
const permissions = ref<Permission[]>([])

// Role form
const roleForm = ref({
  role_name: '',
  description: '',
  permissions: [] as number[],
  status: 1
})

// Role templates from config
const roleTemplates = Object.values(ROLES).map(role => ({
  id: role.id,
  name: role.name,
  description: role.description,
  permissions: role.permissions
}))

// Computed properties
const groupedPermissions = computed(() => {
  const grouped: Record<string, Permission[]> = {}
  
  permissions.value.forEach(permission => {
    const module = permission.module || 'general'
    if (!grouped[module]) {
      grouped[module] = []
    }
    grouped[module].push(permission)
  })
  
  return grouped
})

// Methods
const fetchPermissions = async () => {
  try {
    const response = await systemApi.getPermissions({ limit: 1000 })
    if (response.status === 200) {
      permissions.value = response.message?.data || []
    }
  } catch (error) {
    console.error('Error fetching permissions:', error)
  }
}

const applyTemplate = (template: typeof roleTemplates[0]) => {
  roleForm.value.role_name = template.name
  roleForm.value.description = template.description
  roleForm.value.permissions = [...template.permissions]
}

const isModuleSelected = (modulePermissions: Permission[]) => {
  return modulePermissions.every(permission => 
    roleForm.value.permissions.includes(permission.id)
  )
}

const toggleModule = (modulePermissions: Permission[]) => {
  const allSelected = isModuleSelected(modulePermissions)
  
  if (allSelected) {
    // Remove all permissions from this module
    modulePermissions.forEach(permission => {
      const index = roleForm.value.permissions.indexOf(permission.id)
      if (index > -1) {
        roleForm.value.permissions.splice(index, 1)
      }
    })
  } else {
    // Add all permissions from this module
    modulePermissions.forEach(permission => {
      if (!roleForm.value.permissions.includes(permission.id)) {
        roleForm.value.permissions.push(permission.id)
      }
    })
  }
}

const selectAllPermissions = () => {
  roleForm.value.permissions = permissions.value.map(p => p.id)
}

const clearAllPermissions = () => {
  roleForm.value.permissions = []
}

const saveRole = async () => {
  loading.value = true
  try {
    const response = await systemApi.createRole({
      role_name: roleForm.value.role_name,
      description: roleForm.value.description,
      permissions: roleForm.value.permissions
    })
    
    if (response.status === 200) {
      router.push({ name: 'system-roles' })
    } else {
      console.error('Failed to create role:', response.message)
    }
  } catch (error) {
    console.error('Error creating role:', error)
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push({ name: 'system-roles' })
}

const formatModuleName = (module: string) => {
  return module.charAt(0).toUpperCase() + module.slice(1).replace(/[_-]/g, ' ')
}

// Initialize data
onMounted(() => {
  fetchPermissions()
})
</script>
