<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            {{ isEditMode ? 'Edit Partner' : 'Partner Details' }}
          </h1>
          <p class="text-gray-600 mt-1">
            {{ isEditMode ? 'Update partner information and settings' : 'View partner information and manage settings' }}
          </p>
        </div>
        <div class="flex space-x-3">
          <button
            @click="goBack"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <ArrowLeftIcon class="w-4 h-4 mr-2" />
            Back to Partners
          </button>
          <button
            v-if="!isEditMode && canEditPartner"
            @click="toggleEditMode"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <PencilIcon class="w-4 h-4 mr-2" />
            Edit Partner
          </button>
          <button
            v-if="isEditMode"
            @click="saveChanges"
            :disabled="loading"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200 disabled:opacity-50"
          >
            <CheckIcon class="w-4 h-4 mr-2" />
            {{ loading ? 'Saving...' : 'Save Changes' }}
          </button>
          <button
            v-if="isEditMode"
            @click="cancelEdit"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <XMarkIcon class="w-4 h-4 mr-2" />
            Cancel
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="initialLoading" class="flex items-center justify-center py-12">
      <div class="flex items-center space-x-2 text-gray-500">
        <svg class="animate-spin h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
        <span>Loading partner details...</span>
      </div>
    </div>

    <!-- Partner Information -->
    <div v-else-if="partner" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Partner Information</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Partner Name</label>
          <input
            v-if="isEditMode"
            v-model="partnerForm.name"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          <p v-else class="text-gray-900">{{ partner.name || '-' }}</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
          <input
            v-if="isEditMode"
            v-model="partnerForm.email"
            type="email"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          <p v-else class="text-gray-900">{{ partner.email || '-' }}</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
          <input
            v-if="isEditMode"
            v-model="partnerForm.phone"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          <p v-else class="text-gray-900">{{ partner.phone || '-' }}</p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            v-if="isEditMode"
            v-model="partnerForm.status"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="pending">Pending</option>
            <option value="suspended">Suspended</option>
          </select>
          <span
            v-else
            :class="{
              'bg-green-100 text-green-800': partner.status === 'active',
              'bg-red-100 text-red-800': partner.status === 'inactive',
              'bg-yellow-100 text-yellow-800': partner.status === 'pending',
              'bg-orange-100 text-orange-800': partner.status === 'suspended'
            }"
            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
          >
            {{ partner.status ? partner.status.charAt(0).toUpperCase() + partner.status.slice(1) : '-' }}
          </span>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Partner Type</label>
          <select
            v-if="isEditMode"
            v-model="partnerForm.partner_type"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="betting">Betting</option>
            <option value="payment">Payment</option>
            <option value="technology">Technology</option>
            <option value="marketing">Marketing</option>
          </select>
          <span v-else class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {{ partner.partner_type ? partner.partner_type.charAt(0).toUpperCase() + partner.partner_type.slice(1) : '-' }}
          </span>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Created Date</label>
          <p class="text-gray-900">{{ formatDate(partner.created_at) }}</p>
        </div>
      </div>
    </div>

    <!-- Services and Settings Cards -->
    <div v-if="partner && !initialLoading" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Services Card -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Services</h3>
          <button
            @click="manageServices"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <Cog6ToothIcon class="w-4 h-4 mr-2" />
            Manage
          </button>
        </div>
        
        <div v-if="partnerServices.length > 0" class="space-y-3">
          <div
            v-for="service in partnerServices"
            :key="service.id"
            class="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
          >
            <div>
              <p class="font-medium text-gray-900">{{ service.service_name }}</p>
              <p class="text-sm text-gray-500">Rate Limit: {{ service.rate_limit_per_minute }}/min</p>
            </div>
            <span
              :class="{
                'bg-green-100 text-green-800': service.status === 'active',
                'bg-red-100 text-red-800': service.status === 'inactive'
              }"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
            >
              {{ service.status }}
            </span>
          </div>
        </div>
        <div v-else class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No services configured</h3>
          <p class="mt-1 text-sm text-gray-500">Get started by adding services for this partner.</p>
        </div>
      </div>

      <!-- Settings Card -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">Settings</h3>
          <button
            @click="manageSettings"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            <Cog6ToothIcon class="w-4 h-4 mr-2" />
            Manage
          </button>
        </div>
        
        <div v-if="partnerSettings" class="space-y-3">
          <div class="grid grid-cols-1 gap-3">
            <div class="p-3 border border-gray-200 rounded-lg">
              <p class="text-sm font-medium text-gray-700">API Key</p>
              <p class="text-sm text-gray-900 font-mono">{{ maskApiKey(partnerSettings.api_key) }}</p>
            </div>
            <div class="p-3 border border-gray-200 rounded-lg">
              <p class="text-sm font-medium text-gray-700">Currency</p>
              <p class="text-sm text-gray-900">{{ partnerSettings.currency || '-' }}</p>
            </div>
            <div class="p-3 border border-gray-200 rounded-lg">
              <p class="text-sm font-medium text-gray-700">Billing Mode</p>
              <p class="text-sm text-gray-900">{{ partnerSettings.billing_mode || '-' }}</p>
            </div>
            <div class="p-3 border border-gray-200 rounded-lg">
              <p class="text-sm font-medium text-gray-700">Rate Limit</p>
              <p class="text-sm text-gray-900">{{ partnerSettings.rate_limit || '-' }}/min</p>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No settings configured</h3>
          <p class="mt-1 text-sm text-gray-500">Configure API settings and preferences for this partner.</p>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="!initialLoading && !partner" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"/>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">Partner not found</h3>
      <p class="mt-1 text-sm text-gray-500">The partner you're looking for doesn't exist or has been removed.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ArrowLeftIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  Cog6ToothIcon
} from '@heroicons/vue/24/outline'
import { partnerApi, type Partner, type PartnerSettings } from '@/services/partnerApi'
import { useAuthStore } from '@/stores/auth'
import { useFormData } from '@/composables/useFormData'
import { formatDate } from '@/utils/formatters'

// Router and stores
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const { prefillForm, storeFormData, clearFormData } = useFormData()

// Reactive data
const loading = ref(false)
const initialLoading = ref(true)
const partner = ref<Partner | null>(null)
const partnerSettings = ref<PartnerSettings | null>(null)
const partnerServices = ref<any[]>([])

// Edit mode
const isEditMode = computed(() => route.query.mode === 'edit')

// Permissions
const canEditPartner = computed(() => {
  return authStore.hasAnyRole([1, 2]) || authStore.isSuperUser
})

// Partner form for editing
const partnerForm = reactive({
  name: '',
  email: '',
  phone: '',
  status: 'active',
  partner_type: 'betting'
})

// Methods
const fetchPartner = async () => {
  const partnerId = route.params.id as string
  if (!partnerId) {
    router.push({ name: 'partners' })
    return
  }

  // First try to get cached data
  const cachedPartner = prefillForm({}, 'partner')
  if (cachedPartner && cachedPartner.id === partnerId) {
    partner.value = cachedPartner
    populateForm(cachedPartner)
    return
  }

  // If no cached data, fetch from API
  try {
    const response = await partnerApi.getPartners({
      partner_id: partnerId,
      limit: 1
    })

    if (response.status === 200 && response.message.data.length > 0) {
      partner.value = response.message.data[0]

      // Store for future use
      storeFormData(partner.value, 'partner')
      populateForm(partner.value)
    } else {
      partner.value = null
    }
  } catch (error) {
    console.error('Error fetching partner:', error)
    partner.value = null
  }
}

const populateForm = (partnerData: any) => {
  if (partnerData) {
    partnerForm.name = partnerData.name || ''
    partnerForm.email = partnerData.email || ''
    partnerForm.phone = partnerData.phone || ''
    partnerForm.status = partnerData.status || 'active'
    partnerForm.partner_type = partnerData.partner_type || 'betting'
  }
}

const fetchPartnerSettings = async () => {
  const partnerId = route.params.id as string
  if (!partnerId) return

  try {
    // This would need a specific API endpoint for partner settings
    // For now, we'll use a placeholder
    partnerSettings.value = null
  } catch (error) {
    console.error('Error fetching partner settings:', error)
  }
}

const fetchPartnerServices = async () => {
  const partnerId = route.params.id as string
  if (!partnerId) return

  try {
    // This would need a specific API endpoint for partner services
    // For now, we'll use a placeholder
    partnerServices.value = []
  } catch (error) {
    console.error('Error fetching partner services:', error)
  }
}

const toggleEditMode = () => {
  router.push({
    name: 'partner-details',
    params: { id: route.params.id },
    query: { mode: 'edit' }
  })
}

const cancelEdit = () => {
  router.push({
    name: 'partner-details',
    params: { id: route.params.id }
  })
}

const saveChanges = async () => {
  loading.value = true
  try {
    // This would need an update partner API endpoint
    // For now, just navigate back to view mode
    await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call

    router.push({
      name: 'partner-details',
      params: { id: route.params.id }
    })
  } catch (error) {
    console.error('Error saving partner:', error)
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push({ name: 'partners' })
}

const manageServices = () => {
  router.push({
    name: 'partner-services',
    query: { partner_id: route.params.id }
  })
}

const manageSettings = () => {
  // Navigate to partner settings page or open modal
  console.log('Manage settings for partner:', route.params.id)
}

const maskApiKey = (apiKey: string) => {
  if (!apiKey) return '-'
  if (apiKey.length <= 8) return apiKey
  return apiKey.substring(0, 4) + '****' + apiKey.substring(apiKey.length - 4)
}

// Lifecycle
onMounted(async () => {
  await Promise.all([
    fetchPartner(),
    fetchPartnerSettings(),
    fetchPartnerServices()
  ])
  initialLoading.value = false
})
</script>
