import { onMounted, onUnmounted } from 'vue'
import { useDataCacheStore } from '@/stores/dataCache'

/**
 * Data refresh management composable
 * <PERSON>les browser refresh, pull-to-refresh, and periodic data updates
 */
export function useDataRefresh() {
  const dataCacheStore = useDataCacheStore()

  // Refresh interval (5 minutes)
  const REFRESH_INTERVAL = 5 * 60 * 1000
  let refreshInterval: NodeJS.Timeout | null = null

  /**
   * Handle browser refresh/reload
   */
  const handleBeforeUnload = () => {
    // Data will be preserved in Pinia store, but we can add cleanup here if needed
  }

  /**
   * Handle visibility change (tab focus/blur)
   */
  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible') {
      // Tab became visible, refresh data if cache is stale
      refreshStaleData()
    }
  }

  /**
   * Handle online/offline status
   */
  const handleOnline = () => {
    // <PERSON><PERSON><PERSON> came back online, refresh all data
    dataCacheStore.refreshAllData()
  }

  /**
   * Refresh data that has become stale
   */
  const refreshStaleData = async () => {
    const promises = []

    if (!dataCacheStore.isRolesCacheValid) {
      promises.push(dataCacheStore.fetchRoles(true))
    }

    if (!dataCacheStore.isPermissionsCacheValid) {
      promises.push(dataCacheStore.fetchPermissions(true))
    }

    if (!dataCacheStore.isUsersCacheValid) {
      promises.push(dataCacheStore.fetchUsers(true))
    }

    if (!dataCacheStore.isPartnersCacheValid) {
      promises.push(dataCacheStore.fetchPartners(true))
    }

    if (promises.length > 0) {
      await Promise.all(promises)
    }
  }

  /**
   * Force refresh all data
   */
  const forceRefreshAll = async () => {
    await dataCacheStore.refreshAllData()
  }

  /**
   * Start periodic refresh
   */
  const startPeriodicRefresh = () => {
    if (refreshInterval) {
      clearInterval(refreshInterval)
    }

    refreshInterval = setInterval(() => {
      refreshStaleData()
    }, REFRESH_INTERVAL)
  }

  /**
   * Stop periodic refresh
   */
  const stopPeriodicRefresh = () => {
    if (refreshInterval) {
      clearInterval(refreshInterval)
      refreshInterval = null
    }
  }

  /**
   * Handle pull-to-refresh gesture (for mobile)
   */
  const handlePullToRefresh = async () => {
    await forceRefreshAll()
  }

  /**
   * Setup event listeners
   */
  const setupEventListeners = () => {
    // Browser refresh/reload
    window.addEventListener('beforeunload', handleBeforeUnload)
    
    // Tab visibility change
    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    // Online/offline status
    window.addEventListener('online', handleOnline)
    
    // Start periodic refresh
    startPeriodicRefresh()
  }

  /**
   * Cleanup event listeners
   */
  const cleanupEventListeners = () => {
    window.removeEventListener('beforeunload', handleBeforeUnload)
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('online', handleOnline)
    stopPeriodicRefresh()
  }

  /**
   * Initialize data refresh system
   */
  const initializeDataRefresh = async () => {
    // Load initial data if not already cached
    await refreshStaleData()
    
    // Setup event listeners
    setupEventListeners()
  }

  // Lifecycle hooks
  onMounted(() => {
    initializeDataRefresh()
  })

  onUnmounted(() => {
    cleanupEventListeners()
  })

  return {
    // Methods
    refreshStaleData,
    forceRefreshAll,
    handlePullToRefresh,
    startPeriodicRefresh,
    stopPeriodicRefresh,
    
    // State
    isOnline: navigator.onLine
  }
}

/**
 * Pull-to-refresh directive for Vue components
 */
export const vPullToRefresh = {
  mounted(el: HTMLElement, binding: any) {
    let startY = 0
    let currentY = 0
    let isPulling = false
    const threshold = 100 // pixels to trigger refresh

    const handleTouchStart = (e: TouchEvent) => {
      if (el.scrollTop === 0) {
        startY = e.touches[0].clientY
        isPulling = true
      }
    }

    const handleTouchMove = (e: TouchEvent) => {
      if (!isPulling) return

      currentY = e.touches[0].clientY
      const pullDistance = currentY - startY

      if (pullDistance > 0 && pullDistance < threshold) {
        // Show pull indicator
        el.style.transform = `translateY(${pullDistance / 3}px)`
        el.style.transition = 'none'
      }
    }

    const handleTouchEnd = async () => {
      if (!isPulling) return

      const pullDistance = currentY - startY
      isPulling = false

      if (pullDistance > threshold) {
        // Trigger refresh
        if (binding.value && typeof binding.value === 'function') {
          await binding.value()
        }
      }

      // Reset position
      el.style.transform = 'translateY(0)'
      el.style.transition = 'transform 0.3s ease'
    }

    el.addEventListener('touchstart', handleTouchStart, { passive: true })
    el.addEventListener('touchmove', handleTouchMove, { passive: true })
    el.addEventListener('touchend', handleTouchEnd, { passive: true })

    // Store cleanup function
    ;(el as any)._pullToRefreshCleanup = () => {
      el.removeEventListener('touchstart', handleTouchStart)
      el.removeEventListener('touchmove', handleTouchMove)
      el.removeEventListener('touchend', handleTouchEnd)
    }
  },

  unmounted(el: HTMLElement) {
    if ((el as any)._pullToRefreshCleanup) {
      ;(el as any)._pullToRefreshCleanup()
    }
  }
}
