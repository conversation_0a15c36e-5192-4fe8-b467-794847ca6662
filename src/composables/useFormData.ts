import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

/**
 * Form data management store
 * Stores form data for edit pages to prefill forms
 */
export const useFormDataStore = defineStore('formData', () => {
  // Stored form data
  const userData = ref<any>(null)
  const roleData = ref<any>(null)
  const partnerData = ref<any>(null)
  const serviceData = ref<any>(null)
  const permissionData = ref<any>(null)

  // User form data management
  const setUserData = (data: any) => {
    userData.value = { ...data }
  }

  const getUserData = () => {
    return userData.value
  }

  const clearUserData = () => {
    userData.value = null
  }

  // Role form data management
  const setRoleData = (data: any) => {
    roleData.value = { ...data }
  }

  const getRoleData = () => {
    return roleData.value
  }

  const clearRoleData = () => {
    roleData.value = null
  }

  // Partner form data management
  const setPartnerData = (data: any) => {
    partnerData.value = { ...data }
  }

  const getPartnerData = () => {
    return partnerData.value
  }

  const clearPartnerData = () => {
    partnerData.value = null
  }

  // Service form data management
  const setServiceData = (data: any) => {
    serviceData.value = { ...data }
  }

  const getServiceData = () => {
    return serviceData.value
  }

  const clearServiceData = () => {
    serviceData.value = null
  }

  // Permission form data management
  const setPermissionData = (data: any) => {
    permissionData.value = { ...data }
  }

  const getPermissionData = () => {
    return permissionData.value
  }

  const clearPermissionData = () => {
    permissionData.value = null
  }

  // Clear all form data
  const clearAllFormData = () => {
    userData.value = null
    roleData.value = null
    partnerData.value = null
    serviceData.value = null
    permissionData.value = null
  }

  return {
    // State
    userData: computed(() => userData.value),
    roleData: computed(() => roleData.value),
    partnerData: computed(() => partnerData.value),
    serviceData: computed(() => serviceData.value),
    permissionData: computed(() => permissionData.value),

    // User methods
    setUserData,
    getUserData,
    clearUserData,

    // Role methods
    setRoleData,
    getRoleData,
    clearRoleData,

    // Partner methods
    setPartnerData,
    getPartnerData,
    clearPartnerData,

    // Service methods
    setServiceData,
    getServiceData,
    clearServiceData,

    // Permission methods
    setPermissionData,
    getPermissionData,
    clearPermissionData,

    // Global methods
    clearAllFormData
  }
})

/**
 * Form data management composable
 * Provides easy access to form data store
 */
export function useFormData() {
  const formDataStore = useFormDataStore()

  /**
   * Prefill form with stored data
   */
  const prefillForm = <T>(formRef: any, dataType: 'user' | 'role' | 'partner' | 'service' | 'permission'): T | null => {
    let storedData = null

    switch (dataType) {
      case 'user':
        storedData = formDataStore.getUserData()
        break
      case 'role':
        storedData = formDataStore.getRoleData()
        break
      case 'partner':
        storedData = formDataStore.getPartnerData()
        break
      case 'service':
        storedData = formDataStore.getServiceData()
        break
      case 'permission':
        storedData = formDataStore.getPermissionData()
        break
    }

    if (storedData && formRef.value) {
      // Deep merge stored data with form
      Object.keys(storedData).forEach(key => {
        if (formRef.value.hasOwnProperty(key)) {
          formRef.value[key] = storedData[key]
        }
      })
    }

    return storedData
  }

  /**
   * Store form data before navigation
   */
  const storeFormData = (data: any, dataType: 'user' | 'role' | 'partner' | 'service' | 'permission') => {
    switch (dataType) {
      case 'user':
        formDataStore.setUserData(data)
        break
      case 'role':
        formDataStore.setRoleData(data)
        break
      case 'partner':
        formDataStore.setPartnerData(data)
        break
      case 'service':
        formDataStore.setServiceData(data)
        break
      case 'permission':
        formDataStore.setPermissionData(data)
        break
    }
  }

  /**
   * Clear specific form data
   */
  const clearFormData = (dataType: 'user' | 'role' | 'partner' | 'service' | 'permission') => {
    switch (dataType) {
      case 'user':
        formDataStore.clearUserData()
        break
      case 'role':
        formDataStore.clearRoleData()
        break
      case 'partner':
        formDataStore.clearPartnerData()
        break
      case 'service':
        formDataStore.clearServiceData()
        break
      case 'permission':
        formDataStore.clearPermissionData()
        break
    }
  }

  /**
   * Navigate to a page with cached data
   * This method stores data and navigates in one call
   */
  const navigateWithData = (
    router: any,
    data: any,
    dataType: 'user' | 'role' | 'partner' | 'service' | 'permission',
    routeName: string,
    routeParams?: any,
    routeQuery?: any
  ) => {
    // Store the data first
    storeFormData(data, dataType)

    // Then navigate
    const routeConfig: any = { name: routeName }
    if (routeParams) routeConfig.params = routeParams
    if (routeQuery) routeConfig.query = routeQuery

    router.push(routeConfig)
  }

  /**
   * Get cached data for a specific type and ID
   * Returns null if no data or ID doesn't match
   */
  const getCachedData = (
    dataType: 'user' | 'role' | 'partner' | 'service' | 'permission',
    expectedId?: string | number
  ) => {
    let cachedData = null

    switch (dataType) {
      case 'user':
        cachedData = formDataStore.getUserData()
        break
      case 'role':
        cachedData = formDataStore.getRoleData()
        break
      case 'partner':
        cachedData = formDataStore.getPartnerData()
        break
      case 'service':
        cachedData = formDataStore.getServiceData()
        break
      case 'permission':
        cachedData = formDataStore.getPermissionData()
        break
    }

    // If expectedId is provided, check if it matches
    if (expectedId && cachedData && cachedData.id) {
      const cachedId = cachedData.id
      if (cachedId != expectedId && cachedId != parseInt(expectedId as string)) {
        return null // ID doesn't match
      }
    }

    return cachedData
  }

  return {
    // Store access
    formDataStore,

    // Methods
    prefillForm,
    storeFormData,
    clearFormData,

    // Enhanced methods
    navigateWithData,
    getCachedData,

    // Direct access to stored data
    userData: computed(() => formDataStore.userData),
    roleData: computed(() => formDataStore.roleData),
    partnerData: computed(() => formDataStore.partnerData),
    serviceData: computed(() => formDataStore.serviceData),
    permissionData: computed(() => formDataStore.permissionData)
  }
}
