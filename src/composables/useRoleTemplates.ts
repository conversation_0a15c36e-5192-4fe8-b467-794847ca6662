import { computed } from 'vue'
import { useDataCacheStore } from '@/stores/dataCache'

/**
 * Role template management composable
 * Handles role templates and permission parsing using cached data
 */
export function useRoleTemplates() {
  // Use data cache store
  const dataCacheStore = useDataCacheStore()

  /**
   * Fetch roles from cache or API
   */
  const fetchRoleTemplates = async (forceRefresh = false): Promise<void> => {
    await dataCacheStore.fetchRoles(forceRefresh)
  }

  /**
   * Get role template by ID
   */
  const getRoleTemplateById = (roleId: number | string) => {
    return dataCacheStore.getRoleById(roleId)
  }

  /**
   * Parse permissions ACL string to array of permission IDs
   * Supports both comma and colon separated formats
   */
  const parsePermissionsAcl = (permissionsAcl: string): number[] => {
    if (!permissionsAcl) return []

    const separator = permissionsAcl.includes(':') ? ':' : ','
    return permissionsAcl
      .split(separator)
      .map(id => parseInt(id.trim()))
      .filter(id => !isNaN(id))
  }

  /**
   * Get permissions for a specific role template
   */
  const getRolePermissions = (roleId: number | string): number[] => {
    const role = getRoleTemplateById(roleId)
    if (!role || !role.permissions_acl) return []

    return parsePermissionsAcl(role.permissions_acl)
  }

  /**
   * Get role template options for select dropdown
   */
  const roleTemplateOptions = computed(() => {
    return dataCacheStore.roles.data.map(role => ({
      value: role.role_id || parseInt(role.id || '0'),
      text: role.role_name || role.name || 'Unknown Role',
      description: role.description || '',
      permissions_count: parsePermissionsAcl(role.permissions_acl || '').length
    }))
  })

  /**
   * Check if role templates are loaded
   */
  const hasRoleTemplates = computed(() => {
    return dataCacheStore.roles.data.length > 0
  })

  /**
   * Get role template by name
   */
  const getRoleTemplateByName = (name: string) => {
    return dataCacheStore.roles.data.find(role =>
      (role.role_name || role.name || '').toLowerCase() === name.toLowerCase()
    ) || null
  }

  /**
   * Check if a role is admin type
   */
  const isAdminRole = (roleId: number | string): boolean => {
    const role = getRoleTemplateById(roleId)
    if (!role) return false

    const roleName = (role.role_name || role.name || '').toLowerCase()
    return roleName.includes('admin') || roleName.includes('system')
  }

  /**
   * Check if a role is partner type
   */
  const isPartnerRole = (roleId: number | string): boolean => {
    const role = getRoleTemplateById(roleId)
    if (!role) return false

    const roleName = (role.role_name || role.name || '').toLowerCase()
    return roleName.includes('partner')
  }

  /**
   * Get user type based on role
   */
  const getUserTypeFromRole = (roleId: number | string): 'Admin' | 'Partner' => {
    return isAdminRole(roleId) ? 'Admin' : 'Partner'
  }

  return {
    // State
    roleTemplates: computed(() => dataCacheStore.roles.data),
    loading: computed(() => dataCacheStore.roles.loading),
    error: computed(() => null),

    // Computed
    roleTemplateOptions,
    hasRoleTemplates,

    // Methods
    fetchRoleTemplates,
    getRoleTemplateById,
    getRoleTemplateByName,
    parsePermissionsAcl,
    getRolePermissions,
    isAdminRole,
    isPartnerRole,
    getUserTypeFromRole
  }
}
