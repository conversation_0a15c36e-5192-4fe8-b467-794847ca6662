<template>
  <div class="bg-white rounded-xl shadow-sm border border-gray-200">
    <!-- <PERSON>er with Steps -->
    <div class="px-6 py-4 border-b border-gray-200">
      <nav aria-label="Progress">
        <ol class="flex items-center">
          <li
            v-for="(step, index) in steps"
            :key="step.id"
            :class="[
              index !== steps.length - 1 ? 'pr-8 sm:pr-20' : '',
              'relative'
            ]"
          >
            <!-- Step Connector Line -->
            <div
              v-if="index !== steps.length - 1"
              class="absolute inset-0 flex items-center"
              aria-hidden="true"
            >
              <div
                :class="[
                  index < currentStepIndex ? 'bg-blue-600' : 'bg-gray-200',
                  'h-0.5 w-full'
                ]"
              />
            </div>

            <!-- Step Circle and Content -->
            <div class="relative flex items-center">
              <!-- Step Number/Icon -->
              <div
                :class="[
                  index < currentStepIndex
                    ? 'bg-blue-600 border-blue-600'
                    : index === currentStepIndex
                    ? 'bg-blue-600 border-blue-600'
                    : 'bg-white border-gray-300',
                  'flex h-8 w-8 items-center justify-center rounded-full border-2'
                ]"
              >
                <!-- Completed Step -->
                <CheckIcon
                  v-if="index < currentStepIndex"
                  class="h-4 w-4 text-white"
                />
                <!-- Current Step -->
                <span
                  v-else-if="index === currentStepIndex"
                  class="text-white text-sm font-medium"
                >
                  {{ index + 1 }}
                </span>
                <!-- Future Step -->
                <span
                  v-else
                  class="text-gray-500 text-sm font-medium"
                >
                  {{ index + 1 }}
                </span>
              </div>

              <!-- Step Label -->
              <div class="ml-3">
                <p
                  :class="[
                    index <= currentStepIndex ? 'text-gray-900' : 'text-gray-500',
                    'text-sm font-medium'
                  ]"
                >
                  {{ step.title }}
                </p>
                <p
                  v-if="step.description"
                  :class="[
                    index <= currentStepIndex ? 'text-gray-600' : 'text-gray-400',
                    'text-xs'
                  ]"
                >
                  {{ step.description }}
                </p>
              </div>
            </div>
          </li>
        </ol>
      </nav>
    </div>

    <!-- Wizard Content -->
    <div class="px-6 py-6">
      <!-- Current Step Content -->
      <div class="mb-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-2">
          {{ currentStep?.title }}
        </h2>
        <p v-if="currentStep?.description" class="text-gray-600 text-sm mb-4">
          {{ currentStep.description }}
        </p>
        
        <!-- Step Content Slot -->
        <slot :name="`step-${currentStep?.id}`" :step="currentStep" :stepIndex="currentStepIndex">
          <div class="text-center py-8 text-gray-500">
            No content defined for this step
          </div>
        </slot>
      </div>

      <!-- Wizard Navigation -->
      <div class="flex items-center justify-between pt-6 border-t border-gray-200">
        <button
          v-if="currentStepIndex > 0"
          @click="previousStep"
          :disabled="loading"
          class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          <ArrowLeftIcon class="w-4 h-4 mr-2" />
          Previous
        </button>
        <div v-else></div>

        <div class="flex space-x-3">
          <button
            v-if="currentStepIndex < steps.length - 1"
            @click="nextStep"
            :disabled="loading || !canProceed"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            Next
            <ArrowRightIcon class="w-4 h-4 ml-2" />
          </button>
          <button
            v-else
            @click="finish"
            :disabled="loading || !canFinish"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <CheckIcon class="w-4 h-4 mr-2" />
            {{ finishButtonText }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  CheckIcon, 
  ArrowLeftIcon, 
  ArrowRightIcon 
} from '@heroicons/vue/24/outline'

// Types
export interface WizardStep {
  id: string
  title: string
  description?: string
  required?: boolean
}

// Props
interface Props {
  steps: WizardStep[]
  initialStep?: number
  loading?: boolean
  canProceed?: boolean
  canFinish?: boolean
  finishButtonText?: string
}

const props = withDefaults(defineProps<Props>(), {
  initialStep: 0,
  loading: false,
  canProceed: true,
  canFinish: true,
  finishButtonText: 'Finish'
})

// Emits
const emit = defineEmits<{
  'step-change': [stepIndex: number, step: WizardStep]
  'next': [currentStep: WizardStep, nextStep: WizardStep]
  'previous': [currentStep: WizardStep, previousStep: WizardStep]
  'finish': [currentStep: WizardStep]
}>()

// Reactive data
const currentStepIndex = ref(props.initialStep)

// Computed
const currentStep = computed(() => props.steps[currentStepIndex.value])

// Methods
const nextStep = () => {
  if (currentStepIndex.value < props.steps.length - 1) {
    const currentStepData = props.steps[currentStepIndex.value]
    const nextStepData = props.steps[currentStepIndex.value + 1]
    
    emit('next', currentStepData, nextStepData)
    currentStepIndex.value++
    emit('step-change', currentStepIndex.value, currentStep.value)
  }
}

const previousStep = () => {
  if (currentStepIndex.value > 0) {
    const currentStepData = props.steps[currentStepIndex.value]
    const previousStepData = props.steps[currentStepIndex.value - 1]
    
    emit('previous', currentStepData, previousStepData)
    currentStepIndex.value--
    emit('step-change', currentStepIndex.value, currentStep.value)
  }
}

const finish = () => {
  emit('finish', currentStep.value)
}

const goToStep = (stepIndex: number) => {
  if (stepIndex >= 0 && stepIndex < props.steps.length) {
    currentStepIndex.value = stepIndex
    emit('step-change', currentStepIndex.value, currentStep.value)
  }
}

// Expose methods for parent component
defineExpose({
  goToStep,
  nextStep,
  previousStep,
  currentStepIndex: computed(() => currentStepIndex.value),
  currentStep
})
</script>
