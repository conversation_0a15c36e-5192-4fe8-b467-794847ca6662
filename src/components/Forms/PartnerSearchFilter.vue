<template>
  <div class="bg-white p-4 rounded-lg border border-gray-200 space-y-4">
    <h3 class="text-lg font-medium text-gray-900">Search & Filter Partners</h3>
    
    <!-- Search Fields -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Partner Name Search -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Partner Name</label>
        <input
          v-model="filters.name"
          type="text"
          placeholder="Search by name..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
        />
      </div>

      <!-- Country Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Country</label>
        <input
          v-model="filters.country"
          type="text"
          placeholder="Filter by country..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
        />
      </div>

      <!-- Status Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
        <select
          v-model="filters.status"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
        >
          <option value="">All Status</option>
          <option value="1">Active</option>
          <option value="0">Inactive</option>
        </select>
      </div>

      <!-- Phone Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
        <input
          v-model="filters.phone"
          type="text"
          placeholder="Filter by phone..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
        />
      </div>
    </div>

    <!-- Date Range Filter -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
      <DateRangePicker
        v-model="dateRange"
        @range-changed="onDateRangeChanged"
      />
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
      <div class="flex items-center space-x-4">
        <button
          @click="applyFilters"
          class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Apply Filters
        </button>
        <button
          @click="clearAllFilters"
          class="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
        >
          Clear All
        </button>
      </div>
      
      <!-- Export Buttons -->
      <div class="flex items-center space-x-2" v-if="showExportButtons">
        <button
          @click="exportLocal"
          class="px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          Export Local (CSV)
        </button>
        <button
          @click="exportApi"
          class="px-3 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
        >
          Export
        </button>
      </div>
    </div>

    <!-- Active Filters Display -->
    <div v-if="hasActiveFilters" class="pt-2 border-t border-gray-100">
      <div class="flex flex-wrap gap-2">
        <span class="text-sm text-gray-600">Active filters:</span>
        <span
          v-for="(value, key) in activeFilters"
          :key="key"
          class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
        >
          {{ getFilterLabel(key) }}: {{ value }}
          <button
            @click="removeFilter(key)"
            class="ml-1 text-blue-600 hover:text-blue-800 focus:outline-none"
          >
            ×
          </button>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import DateRangePicker from './DateRangePicker.vue'

// Props
interface Props {
  modelValue?: {
    name?: string
    country?: string
    status?: string
    phone?: string
    start_date?: string
    end_date?: string
  }
  showExportButtons?: boolean
  data?: any[] // For local export
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({}),
  showExportButtons: false,
  data: () => []
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any]
  'filters-applied': [filters: any]
  'export-local': [data: any[]]
  'export-api': [filters: any]
}>()

// Local state
const filters = ref({
  name: props.modelValue.name || '',
  country: props.modelValue.country || '',
  status: props.modelValue.status || '',
  phone: props.modelValue.phone || ''
})

const dateRange = ref({
  start: props.modelValue.start_date || '',
  end: props.modelValue.end_date || ''
})

// Computed
const hasActiveFilters = computed(() => {
  return Object.values(activeFilters.value).some(value => value !== '')
})

const activeFilters = computed(() => {
  const active: Record<string, string> = {}
  
  if (filters.value.name) active.name = filters.value.name
  if (filters.value.country) active.country = filters.value.country
  if (filters.value.status) active.status = filters.value.status === '1' ? 'Active' : 'Inactive'
  if (filters.value.phone) active.phone = filters.value.phone
  if (dateRange.value.start) active.start_date = dateRange.value.start
  if (dateRange.value.end) active.end_date = dateRange.value.end
  
  return active
})

// Methods
const getFilterLabel = (key: string): string => {
  const labels: Record<string, string> = {
    name: 'Name',
    country: 'Country',
    status: 'Status',
    phone: 'Phone',
    start_date: 'Start Date',
    end_date: 'End Date'
  }
  return labels[key] || key
}

const removeFilter = (key: string) => {
  if (key === 'start_date' || key === 'end_date') {
    dateRange.value = { start: '', end: '' }
  } else {
    filters.value[key as keyof typeof filters.value] = ''
  }
  applyFilters()
}

const applyFilters = () => {
  const allFilters = {
    ...filters.value,
    start_date: dateRange.value.start,
    end_date: dateRange.value.end
  }
  
  emit('update:modelValue', allFilters)
  emit('filters-applied', allFilters)
}

const clearAllFilters = () => {
  filters.value = {
    name: '',
    country: '',
    status: '',
    phone: ''
  }
  dateRange.value = { start: '', end: '' }
  applyFilters()
}

const onDateRangeChanged = (start: string, end: string) => {
  dateRange.value = { start, end }
}

const exportLocal = () => {
  emit('export-local', props.data)
}

const exportApi = () => {
  const allFilters = {
    ...filters.value,
    start_date: dateRange.value.start,
    end_date: dateRange.value.end,
    export: 1
  }
  emit('export-api', allFilters)
}

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  filters.value = {
    name: newValue.name || '',
    country: newValue.country || '',
    status: newValue.status || '',
    phone: newValue.phone || ''
  }
  dateRange.value = {
    start: newValue.start_date || '',
    end: newValue.end_date || ''
  }
}, { deep: true })
</script>
