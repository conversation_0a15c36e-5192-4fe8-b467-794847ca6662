<template>
  <div class="space-y-4">
    <!-- Role Template Selection -->
    <div>
      <label for="role-template" class="block text-sm font-medium text-gray-700 mb-2">
        Role Template
        <span class="text-red-500">*</span>
      </label>
      <select
        id="role-template"
        v-model="selectedRoleId"
        @change="handleRoleChange"
        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        :disabled="loading"
        required
      >
        <option value="">Select a role template...</option>
        <option
          v-for="option in roleTemplateOptions"
          :key="option.value"
          :value="option.value"
        >
          {{ option.text }}
          <span v-if="option.description"> - {{ option.description }}</span>
          <span class="text-gray-500"> ({{ option.permissions_count }} permissions)</span>
        </option>
      </select>
      
      <!-- Loading state -->
      <div v-if="loading" class="mt-2 text-sm text-gray-500">
        Loading role templates...
      </div>
      
      <!-- Error state -->
      <div v-if="error" class="mt-2 text-sm text-red-600">
        {{ error }}
      </div>
    </div>

    <!-- Selected Role Details -->
    <div v-if="selectedRole" class="bg-gray-50 p-4 rounded-lg">
      <h4 class="text-sm font-medium text-gray-900 mb-2">Role Details</h4>
      <div class="space-y-2 text-sm">
        <div>
          <span class="font-medium">Name:</span> {{ selectedRole.role_name || selectedRole.name }}
        </div>
        <div v-if="selectedRole.description">
          <span class="font-medium">Description:</span> {{ selectedRole.description }}
        </div>
        <div>
          <span class="font-medium">Type:</span> 
          <span :class="userTypeClass">{{ userType }}</span>
        </div>
        <div>
          <span class="font-medium">Permissions:</span> {{ permissionIds.length }} assigned
        </div>
      </div>
      
      <!-- Permission Preview -->
      <div v-if="showPermissionPreview && permissionIds.length > 0" class="mt-3">
        <button
          @click="togglePermissionPreview"
          class="text-sm text-blue-600 hover:text-blue-800 focus:outline-none"
        >
          {{ showPermissions ? 'Hide' : 'Show' }} Permission IDs
        </button>
        <div v-if="showPermissions" class="mt-2 p-2 bg-white rounded border text-xs">
          <div class="flex flex-wrap gap-1">
            <span
              v-for="permId in permissionIds"
              :key="permId"
              class="inline-block px-2 py-1 bg-blue-100 text-blue-800 rounded"
            >
              {{ permId }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRoleTemplates } from '@/composables/useRoleTemplates'
import type { Role } from '@/services/systemApi'

// Props
interface Props {
  modelValue?: number | string
  showPermissionPreview?: boolean
  autoLoad?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  showPermissionPreview: true,
  autoLoad: true
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: number | string]
  'role-selected': [role: Role | null, permissions: number[], userType: 'Admin' | 'Partner']
  'permissions-changed': [permissions: number[]]
}>()

// Composables
const {
  roleTemplates,
  loading,
  error,
  roleTemplateOptions,
  fetchRoleTemplates,
  getRoleTemplateById,
  getRolePermissions,
  getUserTypeFromRole
} = useRoleTemplates()

// Local state
const selectedRoleId = ref<number | string>(props.modelValue)
const showPermissions = ref(false)

// Computed
const selectedRole = computed(() => {
  if (!selectedRoleId.value) return null
  return getRoleTemplateById(selectedRoleId.value)
})

const permissionIds = computed(() => {
  if (!selectedRoleId.value) return []
  return getRolePermissions(selectedRoleId.value)
})

const userType = computed(() => {
  if (!selectedRoleId.value) return 'Partner'
  return getUserTypeFromRole(selectedRoleId.value)
})

const userTypeClass = computed(() => {
  return userType.value === 'Admin' 
    ? 'text-purple-600 font-medium' 
    : 'text-green-600 font-medium'
})

// Methods
const handleRoleChange = () => {
  emit('update:modelValue', selectedRoleId.value)
  emit('role-selected', selectedRole.value, permissionIds.value, userType.value)
  emit('permissions-changed', permissionIds.value)
}

const togglePermissionPreview = () => {
  showPermissions.value = !showPermissions.value
}

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  selectedRoleId.value = newValue
})

// Watch for permission changes
watch(permissionIds, (newPermissions) => {
  emit('permissions-changed', newPermissions)
})

// Initialize
onMounted(async () => {
  if (props.autoLoad) {
    await fetchRoleTemplates()
  }
})
</script>
