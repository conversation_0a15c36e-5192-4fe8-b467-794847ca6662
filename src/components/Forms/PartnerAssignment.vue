<template>
  <div class="space-y-4">
    <!-- Partner Assignment Section -->
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-2">
        {{ isAdminUser ? 'Assign Partners' : 'Assigned Partner' }}
        <span v-if="isRequired" class="text-red-500">*</span>
      </label>
      
      <!-- Admin User: Multiple Partner Selection -->
      <div v-if="isAdminUser" class="space-y-3">
        <!-- Search and Filter Controls -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search by partner name..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>
          <div>
            <input
              v-model="countryFilter"
              type="text"
              placeholder="Filter by country..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>
          <div class="flex items-center space-x-2">
            <button
              type="button"
              @click="clearFilters"
              class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 focus:outline-none"
            >
              Clear Filters
            </button>
          </div>
        </div>

        <!-- Select All/None Controls -->
        <div class="flex items-center space-x-4 text-sm">
          <button
            type="button"
            @click="selectAllPartners"
            class="text-blue-600 hover:text-blue-800 focus:outline-none"
            :disabled="loading || filteredPartners.length === 0"
          >
            Select All Visible
          </button>
          <button
            type="button"
            @click="clearAllPartners"
            class="text-red-600 hover:text-red-800 focus:outline-none"
            :disabled="loading || selectedPartnerIds.length === 0"
          >
            Clear All
          </button>
          <span class="text-gray-500">
            {{ selectedPartnerIds.length }} of {{ filteredPartners.length }} selected
          </span>
        </div>
        
        <!-- Partner Checkboxes -->
        <div class="max-h-48 overflow-y-auto border border-gray-200 rounded-md p-3 space-y-2">
          <div v-if="loading" class="text-center text-gray-500 py-4">
            Loading partners...
          </div>

          <div v-else-if="filteredPartners.length === 0" class="text-center text-gray-500 py-4">
            {{ availablePartners.length === 0 ? 'No partners available' : 'No partners match your search' }}
          </div>

          <label
            v-else
            v-for="partner in filteredPartners"
            :key="partner.id"
            class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer"
          >
            <input
              type="checkbox"
              :value="partner.id"
              v-model="selectedPartnerIds"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <div class="flex-1">
              <div class="text-sm font-medium text-gray-900">{{ partner.name }}</div>
              <div class="text-xs text-gray-500 space-x-2">
                <span v-if="partner.country">{{ partner.country }}</span>
                <span v-if="partner.address">{{ partner.address }}</span>
                <span v-if="partner.msisdn">{{ partner.msisdn }}</span>
              </div>
            </div>
            <div class="text-xs text-gray-400">
              ID: {{ partner.id }}
            </div>
          </label>
        </div>
      </div>
      
      <!-- Partner User: Read-only Display -->
      <div v-else class="space-y-2">
        <div v-if="loading" class="text-gray-500">
          Loading assigned partners...
        </div>
        
        <div v-else-if="userPartners.length === 0" class="text-gray-500">
          No partners assigned
        </div>
        
        <div v-else class="space-y-2">
          <div
            v-for="partner in userPartners"
            :key="partner.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-md"
          >
            <div>
              <div class="text-sm font-medium text-gray-900">{{ partner.name }}</div>
              <div v-if="partner.description" class="text-xs text-gray-500">
                {{ partner.description }}
              </div>
            </div>
            <div class="text-xs text-gray-400">
              ID: {{ partner.id }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- Error Display -->
      <div v-if="error" class="mt-2 text-sm text-red-600">
        {{ error }}
      </div>
      
      <!-- Validation Error -->
      <div v-if="validationError" class="mt-2 text-sm text-red-600">
        {{ validationError }}
      </div>
    </div>
    
    <!-- Selected Partners Summary -->
    <div v-if="isAdminUser && selectedPartnerIds.length > 0" class="bg-blue-50 p-3 rounded-md">
      <h4 class="text-sm font-medium text-blue-900 mb-2">Selected Partners Summary</h4>
      <div class="flex flex-wrap gap-2">
        <span
          v-for="partnerId in selectedPartnerIds"
          :key="partnerId"
          class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
        >
          {{ getPartnerName(partnerId) }}
          <button
            type="button"
            @click="removePartner(partnerId)"
            class="ml-1 text-blue-600 hover:text-blue-800 focus:outline-none"
          >
            ×
          </button>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { partnerApi, type Partner } from '@/services/partnerApi'
import { useAuthStore } from '@/stores/auth'

// Props
interface Props {
  modelValue?: number[]
  userType?: 'Admin' | 'Partner'
  required?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  userType: 'Partner',
  required: false,
  disabled: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: number[]]
  'partners-changed': [partners: Partner[]]
  'validation-error': [error: string | null]
}>()

// Store
const authStore = useAuthStore()

// Local state
const selectedPartnerIds = ref<number[]>([...props.modelValue])
const availablePartners = ref<Partner[]>([])
const loading = ref(false)
const error = ref<string | null>(null)
const validationError = ref<string | null>(null)
const searchQuery = ref('')
const countryFilter = ref('')

// Computed
const isAdminUser = computed(() => {
  return props.userType === 'Admin' || authStore.isSuperUser
})

const isRequired = computed(() => {
  return props.required && !props.disabled
})

const userPartners = computed(() => {
  if (isAdminUser.value) return []

  // For partner users, show their assigned partners
  const userPartnerIds = authStore.user?.partners?.map(p => p.id) || []
  return availablePartners.value.filter(partner =>
    userPartnerIds.includes(partner.id)
  )
})

const filteredPartners = computed(() => {
  let filtered = availablePartners.value

  // Filter by search query (partner name)
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(partner =>
      partner.name.toLowerCase().includes(query)
    )
  }

  // Filter by country
  if (countryFilter.value) {
    const country = countryFilter.value.toLowerCase()
    filtered = filtered.filter(partner =>
      partner.country && partner.country.toLowerCase().includes(country)
    )
  }

  return filtered
})

// Methods
const fetchPartners = async () => {
  loading.value = true
  error.value = null
  
  try {
    const response = await partnerApi.getPartners({ limit: 1000 })
    
    if (response.status === 200) {
      availablePartners.value = response.message.data || []
      
      // For partner users, auto-select their assigned partners
      if (!isAdminUser.value) {
        const userPartnerIds = authStore.user?.partners?.map(p => p.id) || []
        selectedPartnerIds.value = userPartnerIds
        emit('update:modelValue', selectedPartnerIds.value)
      }
    } else {
      error.value = 'Failed to load partners'
    }
  } catch (err: any) {
    error.value = err.message || 'Failed to load partners'
  } finally {
    loading.value = false
  }
}

const selectAllPartners = () => {
  const visiblePartnerIds = filteredPartners.value.map(p => p.id)
  const newSelection = [...new Set([...selectedPartnerIds.value, ...visiblePartnerIds])]
  selectedPartnerIds.value = newSelection
  emit('update:modelValue', selectedPartnerIds.value)
}

const clearFilters = () => {
  searchQuery.value = ''
  countryFilter.value = ''
}

const clearAllPartners = () => {
  selectedPartnerIds.value = []
  emit('update:modelValue', selectedPartnerIds.value)
}

const removePartner = (partnerId: number) => {
  selectedPartnerIds.value = selectedPartnerIds.value.filter(id => id !== partnerId)
  emit('update:modelValue', selectedPartnerIds.value)
}

const getPartnerName = (partnerId: number): string => {
  const partner = availablePartners.value.find(p => p.id === partnerId)
  return partner?.name || `Partner ${partnerId}`
}

const validateSelection = () => {
  if (isRequired.value && selectedPartnerIds.value.length === 0) {
    validationError.value = 'At least one partner must be selected'
    emit('validation-error', validationError.value)
    return false
  }
  
  validationError.value = null
  emit('validation-error', null)
  return true
}

// Watch for changes
watch(selectedPartnerIds, (newIds) => {
  emit('update:modelValue', newIds)
  
  const selectedPartners = availablePartners.value.filter(p => 
    newIds.includes(p.id)
  )
  emit('partners-changed', selectedPartners)
  
  validateSelection()
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  selectedPartnerIds.value = [...newValue]
})

// Initialize
onMounted(async () => {
  await fetchPartners()
})

// Expose validation method
defineExpose({
  validate: validateSelection
})
</script>
