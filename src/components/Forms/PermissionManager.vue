<template>
  <div class="space-y-6">
    <!-- Permission Assignment Mode -->
    <div class="flex items-center space-x-4 mb-4">
      <label class="text-sm font-medium text-gray-700">Assignment Mode:</label>
      <div class="flex space-x-4">
        <label class="flex items-center">
          <input
            type="radio"
            v-model="assignmentMode"
            value="role"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
          />
          <span class="ml-2 text-sm text-gray-700">Role Template</span>
        </label>
        <label class="flex items-center">
          <input
            type="radio"
            v-model="assignmentMode"
            value="individual"
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
          />
          <span class="ml-2 text-sm text-gray-700">Individual Permissions</span>
        </label>
      </div>
    </div>

    <!-- Role Template Mode -->
    <div v-if="assignmentMode === 'role'" class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Select Role Template
        </label>
        <select
          v-model="selectedRoleId"
          @change="onRoleTemplateChange"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Choose a role template...</option>
          <option
            v-for="role in roleTemplates"
            :key="role.id || role.role_id"
            :value="role.id || role.role_id"
          >
            {{ role.name || role.role_name }}
            <span v-if="role.description"> - {{ role.description }}</span>
          </option>
        </select>
      </div>

      <!-- Role Template Preview -->
      <div v-if="selectedRole" class="bg-blue-50 p-4 rounded-lg">
        <h4 class="text-sm font-medium text-blue-900 mb-2">Template Permissions</h4>
        <div class="text-sm text-blue-800">
          <div><strong>Role:</strong> {{ selectedRole.name || selectedRole.role_name }}</div>
          <div v-if="selectedRole.description"><strong>Description:</strong> {{ selectedRole.description }}</div>
          <div><strong>Permissions:</strong> {{ rolePermissionIds.length }} assigned</div>
        </div>
        <div v-if="rolePermissionIds.length > 0" class="mt-2">
          <div class="flex flex-wrap gap-1">
            <span
              v-for="permId in rolePermissionIds"
              :key="permId"
              class="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
            >
              {{ getPermissionName(permId) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Individual Permission Mode -->
    <div v-if="assignmentMode === 'individual'" class="space-y-4">
      <!-- Search and Filter -->
      <div class="flex items-center space-x-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search permissions..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <select
            v-model="selectedModule"
            class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Modules</option>
            <option v-for="module in availableModules" :key="module" :value="module">
              {{ formatModuleName(module) }}
            </option>
          </select>
        </div>
      </div>

      <!-- Bulk Actions -->
      <div class="flex items-center space-x-4 text-sm">
        <button
          type="button"
          @click="selectAllVisible"
          class="text-blue-600 hover:text-blue-800 focus:outline-none"
          :disabled="loading"
        >
          Select All Visible
        </button>
        <button
          type="button"
          @click="clearAllSelections"
          class="text-red-600 hover:text-red-800 focus:outline-none"
          :disabled="loading"
        >
          Clear All
        </button>
        <span class="text-gray-500">
          {{ selectedPermissionIds.length }} of {{ filteredPermissions.length }} selected
        </span>
      </div>

      <!-- Permissions List -->
      <div class="max-h-96 overflow-y-auto border border-gray-200 rounded-md">
        <div v-if="loading" class="p-4 text-center text-gray-500">
          Loading permissions...
        </div>
        
        <div v-else-if="filteredPermissions.length === 0" class="p-4 text-center text-gray-500">
          No permissions found
        </div>
        
        <div v-else class="divide-y divide-gray-200">
          <label
            v-for="permission in filteredPermissions"
            :key="permission.id"
            class="flex items-center space-x-3 p-3 hover:bg-gray-50 cursor-pointer"
          >
            <input
              type="checkbox"
              :value="permission.id"
              v-model="selectedPermissionIds"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium text-gray-900">{{ permission.name }}</div>
              <div v-if="permission.description" class="text-xs text-gray-500">
                {{ permission.description }}
              </div>
              <div v-if="permission.module" class="text-xs text-blue-600">
                {{ formatModuleName(permission.module) }}
              </div>
            </div>
            <div class="text-xs text-gray-400">
              ID: {{ permission.id }}
            </div>
          </label>
        </div>
      </div>
    </div>

    <!-- Selected Permissions Summary -->
    <div v-if="selectedPermissionIds.length > 0" class="bg-green-50 p-4 rounded-lg">
      <h4 class="text-sm font-medium text-green-900 mb-2">
        Selected Permissions ({{ selectedPermissionIds.length }})
      </h4>
      <div class="flex flex-wrap gap-2">
        <span
          v-for="permId in selectedPermissionIds"
          :key="permId"
          class="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
        >
          {{ getPermissionName(permId) }}
          <button
            type="button"
            @click="removePermission(permId)"
            class="ml-1 text-green-600 hover:text-green-800 focus:outline-none"
          >
            ×
          </button>
        </span>
      </div>
    </div>

    <!-- Permissions ACL Output -->
    <div v-if="showAclOutput" class="bg-gray-50 p-4 rounded-lg">
      <h4 class="text-sm font-medium text-gray-900 mb-2">Permissions ACL</h4>
      <div class="text-sm font-mono bg-white p-2 rounded border">
        {{ permissionsAcl || 'No permissions selected' }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { systemApi, type Permission, type Role } from '@/services/systemApi'
import { useRoleTemplates } from '@/composables/useRoleTemplates'

// Props
interface Props {
  modelValue?: number[]
  showAclOutput?: boolean
  aclSeparator?: ',' | ':'
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  showAclOutput: false,
  aclSeparator: ','
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: number[]]
  'permissions-changed': [permissions: number[], acl: string]
  'acl-changed': [acl: string]
}>()

// Composables
const {
  roleTemplates,
  loading: roleLoading,
  fetchRoleTemplates,
  getRoleTemplateById,
  getRolePermissions,
  parsePermissionsAcl
} = useRoleTemplates()

// Local state
const assignmentMode = ref<'role' | 'individual'>('role')
const selectedPermissionIds = ref<number[]>([...props.modelValue])
const selectedRoleId = ref<number | string>('')
const permissions = ref<Permission[]>([])
const loading = ref(false)
const searchQuery = ref('')
const selectedModule = ref('')

// Computed
const selectedRole = computed(() => {
  if (!selectedRoleId.value) return null
  return getRoleTemplateById(selectedRoleId.value)
})

const rolePermissionIds = computed(() => {
  if (!selectedRoleId.value) return []
  return getRolePermissions(selectedRoleId.value)
})

const filteredPermissions = computed(() => {
  let filtered = permissions.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(p =>
      p.name.toLowerCase().includes(query) ||
      (p.description && p.description.toLowerCase().includes(query))
    )
  }

  if (selectedModule.value) {
    filtered = filtered.filter(p => p.module === selectedModule.value)
  }

  return filtered
})

const availableModules = computed(() => {
  const modules = new Set(permissions.value.map(p => p.module).filter(Boolean))
  return Array.from(modules).sort()
})

const permissionsAcl = computed(() => {
  return selectedPermissionIds.value.join(props.aclSeparator)
})

// Methods
const fetchPermissions = async () => {
  loading.value = true
  try {
    const response = await systemApi.getPermissions({ limit: 1000 })
    if (response.status === 200) {
      permissions.value = response.message.data || []
    }
  } catch (error) {
    console.error('Error fetching permissions:', error)
  } finally {
    loading.value = false
  }
}

const getPermissionName = (permissionId: number): string => {
  const permission = permissions.value.find(p => p.id === permissionId)
  return permission?.name || `Permission ${permissionId}`
}

const formatModuleName = (module: string): string => {
  return module.charAt(0).toUpperCase() + module.slice(1).replace(/[_-]/g, ' ')
}

const onRoleTemplateChange = () => {
  if (assignmentMode.value === 'role' && selectedRoleId.value) {
    selectedPermissionIds.value = [...rolePermissionIds.value]
    emit('update:modelValue', selectedPermissionIds.value)
  }
}

const selectAllVisible = () => {
  const visibleIds = filteredPermissions.value.map(p => p.id)
  const newSelection = [...new Set([...selectedPermissionIds.value, ...visibleIds])]
  selectedPermissionIds.value = newSelection
}

const clearAllSelections = () => {
  selectedPermissionIds.value = []
}

const removePermission = (permissionId: number) => {
  selectedPermissionIds.value = selectedPermissionIds.value.filter(id => id !== permissionId)
}

// Watch for changes
watch(selectedPermissionIds, (newIds) => {
  emit('update:modelValue', newIds)
  emit('permissions-changed', newIds, permissionsAcl.value)
  emit('acl-changed', permissionsAcl.value)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  selectedPermissionIds.value = [...newValue]
})

watch(assignmentMode, (newMode) => {
  if (newMode === 'individual') {
    selectedRoleId.value = ''
  }
})

// Initialize
onMounted(async () => {
  await Promise.all([
    fetchRoleTemplates(),
    fetchPermissions()
  ])
})
</script>
