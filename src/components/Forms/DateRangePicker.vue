<template>
  <div class="space-y-4">
    <!-- Preset Buttons -->
    <div class="flex flex-wrap gap-2">
      <button
        v-for="preset in presets"
        :key="preset.key"
        @click="selectPreset(preset.key)"
        :class="[
          'px-3 py-1 text-sm rounded-md border transition-colors',
          selectedPreset === preset.key
            ? 'bg-blue-600 text-white border-blue-600'
            : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
        ]"
      >
        {{ preset.label }}
      </button>
    </div>

    <!-- Custom Date Range -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
        <input
          v-model="startDate"
          type="date"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
        />
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
        <input
          v-model="endDate"
          type="date"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
        />
      </div>
    </div>

    <!-- Clear Button -->
    <div class="flex justify-end">
      <button
        @click="clearDates"
        class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 focus:outline-none"
      >
        Clear Dates
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// Props
interface Props {
  modelValue?: {
    start: string
    end: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({ start: '', end: '' })
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: { start: string; end: string }]
  'range-changed': [start: string, end: string]
}>()

// Local state
const startDate = ref(props.modelValue.start)
const endDate = ref(props.modelValue.end)
const selectedPreset = ref<string | null>(null)

// Date presets
const presets = [
  {
    key: 'today',
    label: 'Today',
    getDates: () => {
      const today = new Date()
      const dateStr = today.toISOString().split('T')[0]
      return { start: dateStr, end: dateStr }
    }
  },
  {
    key: 'last7days',
    label: 'Last 7 Days',
    getDates: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      return {
        start: start.toISOString().split('T')[0],
        end: end.toISOString().split('T')[0]
      }
    }
  },
  {
    key: 'last14days',
    label: 'Last 14 Days',
    getDates: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 14)
      return {
        start: start.toISOString().split('T')[0],
        end: end.toISOString().split('T')[0]
      }
    }
  },
  {
    key: 'lastMonth',
    label: 'Last Month',
    getDates: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      return {
        start: start.toISOString().split('T')[0],
        end: end.toISOString().split('T')[0]
      }
    }
  },
  {
    key: 'last6months',
    label: 'Last 6 Months',
    getDates: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 6)
      return {
        start: start.toISOString().split('T')[0],
        end: end.toISOString().split('T')[0]
      }
    }
  }
]

// Computed
const dateRange = computed(() => ({
  start: startDate.value,
  end: endDate.value
}))

// Methods
const selectPreset = (presetKey: string) => {
  selectedPreset.value = presetKey
  const preset = presets.find(p => p.key === presetKey)
  if (preset) {
    const dates = preset.getDates()
    startDate.value = dates.start
    endDate.value = dates.end
  }
}

const clearDates = () => {
  startDate.value = ''
  endDate.value = ''
  selectedPreset.value = null
}

// Watch for changes
watch(dateRange, (newRange) => {
  emit('update:modelValue', newRange)
  emit('range-changed', newRange.start, newRange.end)
  
  // Clear preset selection if dates are manually changed
  if (selectedPreset.value) {
    const preset = presets.find(p => p.key === selectedPreset.value)
    if (preset) {
      const presetDates = preset.getDates()
      if (newRange.start !== presetDates.start || newRange.end !== presetDates.end) {
        selectedPreset.value = null
      }
    }
  }
}, { deep: true })

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  startDate.value = newValue.start
  endDate.value = newValue.end
}, { deep: true })
</script>
