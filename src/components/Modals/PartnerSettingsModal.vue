<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
        <form @submit.prevent="handleSubmit">
          <!-- Header -->
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                {{ mode === 'edit' ? 'Edit Partner Settings' : 'Create Partner Settings' }}
              </h3>
              <button
                type="button"
                @click="$emit('close')"
                class="text-gray-400 hover:text-gray-600 focus:outline-none"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
              </button>
            </div>

            <!-- Form Fields -->
            <div class="space-y-6">
              <!-- Partner Selection (for create mode) -->
              <div v-if="mode === 'create'">
                <label class="block text-sm font-medium text-gray-700 mb-1">Partner *</label>
                <select
                  v-model="form.partner_id"
                  required
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select a partner...</option>
                  <option v-for="partner in availablePartners" :key="partner.id" :value="partner.id">
                    {{ partner.name }}
                  </option>
                </select>
              </div>

              <!-- API Configuration -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                  <input
                    v-model="form.api_key"
                    type="text"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Auto-generated if empty"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">IP Address</label>
                  <input
                    v-model="form.ip_address"
                    type="text"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., ***********"
                  />
                </div>
              </div>

              <!-- URLs -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Callback URL</label>
                <input
                  v-model="form.callback_url"
                  type="url"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="https://example.com/callback"
                />
              </div>

              <!-- Financial Settings -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Currency</label>
                  <select
                    v-model="form.currency"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="KES">KES</option>
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Billing Mode</label>
                  <select
                    v-model="form.billing_mode"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="prepay">Prepaid</option>
                    <option value="postpay">Postpaid</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Rate Limit (per minute)</label>
                  <input
                    v-model.number="form.rate_limit"
                    type="number"
                    min="1"
                    max="1000"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <!-- Technical Settings -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Timezone</label>
                  <select
                    v-model="form.timezone"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="Africa/Nairobi">Africa/Nairobi</option>
                    <option value="UTC">UTC</option>
                    <option value="Europe/London">Europe/London</option>
                    <option value="America/New_York">America/New_York</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Version</label>
                  <input
                    v-model="form.version"
                    type="text"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., 1.0"
                  />
                </div>
              </div>

              <!-- Websites -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Authorized Websites</label>
                <div class="space-y-2">
                  <div
                    v-for="(website, index) in form.websites"
                    :key="index"
                    class="flex items-center space-x-2"
                  >
                    <input
                      v-model="form.websites[index]"
                      type="url"
                      class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="https://example.com"
                    />
                    <button
                      type="button"
                      @click="removeWebsite(index)"
                      class="p-2 text-red-600 hover:text-red-800 focus:outline-none"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                      </svg>
                    </button>
                  </div>
                  <button
                    type="button"
                    @click="addWebsite"
                    class="w-full px-3 py-2 border border-dashed border-gray-300 rounded-md text-sm text-gray-600 hover:text-gray-800 hover:border-gray-400 focus:outline-none"
                  >
                    + Add Website
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="submit"
              :disabled="loading"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
            >
              <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ loading ? 'Saving...' : (mode === 'edit' ? 'Update Settings' : 'Create Settings') }}
            </button>
            <button
              type="button"
              @click="$emit('close')"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { partnerApi, type PartnerSettings, type Partner } from '@/services/partnerApi'

// Props
interface Props {
  isOpen: boolean
  partnerSettings?: PartnerSettings | null
  partnerId?: string
  mode: 'create' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  partnerSettings: null,
  partnerId: '',
  mode: 'create'
})

// Emits
const emit = defineEmits<{
  close: []
  saved: [settings: PartnerSettings]
}>()

// State
const loading = ref(false)
const availablePartners = ref<Partner[]>([])

// Form data
const form = reactive({
  partner_id: '',
  api_key: '',
  ip_address: '',
  callback_url: '',
  currency: 'KES',
  billing_mode: 'prepay',
  rate_limit: 60,
  timezone: 'Africa/Nairobi',
  version: '1.0',
  websites: ['']
})

// Methods
const loadPartners = async () => {
  try {
    const response = await partnerApi.getPartners({ limit: 100 })
    if (response.status === 200) {
      availablePartners.value = response.message.data || []
    }
  } catch (error) {
    console.error('Failed to load partners:', error)
  }
}

const addWebsite = () => {
  form.websites.push('')
}

const removeWebsite = (index: number) => {
  form.websites.splice(index, 1)
}

const resetForm = () => {
  Object.assign(form, {
    partner_id: props.partnerId || '',
    api_key: '',
    ip_address: '',
    callback_url: '',
    currency: 'KES',
    billing_mode: 'prepay',
    rate_limit: 60,
    timezone: 'Africa/Nairobi',
    version: '1.0',
    websites: ['']
  })
}

const populateForm = () => {
  if (props.partnerSettings) {
    Object.assign(form, {
      partner_id: props.partnerSettings.partner_id?.toString() || '',
      api_key: props.partnerSettings.api_key || '',
      ip_address: props.partnerSettings.ip_address || '',
      callback_url: props.partnerSettings.callback_url || '',
      currency: props.partnerSettings.currency || 'KES',
      billing_mode: props.partnerSettings.billing_mode || 'prepay',
      rate_limit: props.partnerSettings.rate_limit || 60,
      timezone: props.partnerSettings.timezone || 'Africa/Nairobi',
      version: props.partnerSettings.version || '1.0',
      websites: props.partnerSettings.websites || ['']
    })
  }
}

const handleSubmit = async () => {
  loading.value = true
  
  try {
    const payload = {
      ...form,
      websites: form.websites.filter(w => w.trim() !== '')
    }

    let response
    if (props.mode === 'edit' && props.partnerSettings) {
      response = await partnerApi.updatePartnerSettings(props.partnerSettings.id, payload)
    } else {
      response = await partnerApi.createPartnerSettings(payload)
    }

    if (response.status === 200) {
      emit('saved', response.message)
    } else {
      throw new Error(response.message || 'Failed to save settings')
    }
  } catch (error: any) {
    console.error('Failed to save partner settings:', error)
    alert(error.message || 'Failed to save settings')
  } finally {
    loading.value = false
  }
}

// Watchers
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    if (props.mode === 'edit') {
      populateForm()
    } else {
      resetForm()
    }
  }
})

// Lifecycle
onMounted(() => {
  loadPartners()
})
</script>
