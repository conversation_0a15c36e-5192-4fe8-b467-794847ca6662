import type { App } from 'vue'
import { useDataRefresh, vPullToRefresh } from '@/composables/useDataRefresh'

/**
 * Data refresh plugin
 * Provides global data refresh functionality and pull-to-refresh directive
 */
export default {
  install(app: App) {
    // Add pull-to-refresh directive globally
    app.directive('pull-to-refresh', vPullToRefresh)
    
    // Add global properties for data refresh
    app.config.globalProperties.$dataRefresh = useDataRefresh
    
    // Provide data refresh composable globally
    app.provide('dataRefresh', useDataRefresh)
  }
}
