import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { system<PERSON><PERSON> } from '@/services/systemApi'
import { partner<PERSON><PERSON> } from '@/services/partnerApi'

// Types
interface CachedData<T> {
  data: T[]
  lastFetched: number
  loading: boolean
}

interface Role {
  id: string
  role_id: number
  name: string
  role_name: string
  description: string
  permissions_acl: string
  status: string
}

interface Permission {
  id: number
  name: string
  description?: string
  module?: string
  status: number
}

interface User {
  user_id: string
  username: string
  display_name?: string
  user_name?: string
  email_address: string
  msisdn: string
  role_id: number
  role_name?: string
  user_type?: 'Admin' | 'Partner'
  status: number
  permissions?: Permission[]
  partners?: any[]
  created_at?: string
  last_login?: string
}

interface Partner {
  id: number
  name: string
  status: string
  created_at: string
  address?: string
  country?: string
  msisdn?: string
  email_address?: string
}

export const useDataCacheStore = defineStore('dataCache', () => {
  // Cache duration in milliseconds (5 minutes)
  const CACHE_DURATION = 5 * 60 * 1000

  // Cached data
  const roles = ref<CachedData<Role>>({
    data: [],
    lastFetched: 0,
    loading: false
  })

  const permissions = ref<CachedData<Permission>>({
    data: [],
    lastFetched: 0,
    loading: false
  })

  const users = ref<CachedData<User>>({
    data: [],
    lastFetched: 0,
    loading: false
  })

  const partners = ref<CachedData<Partner>>({
    data: [],
    lastFetched: 0,
    loading: false
  })

  // Computed
  const isRolesCacheValid = computed(() => {
    return Date.now() - roles.value.lastFetched < CACHE_DURATION
  })

  const isPermissionsCacheValid = computed(() => {
    return Date.now() - permissions.value.lastFetched < CACHE_DURATION
  })

  const isUsersCacheValid = computed(() => {
    return Date.now() - users.value.lastFetched < CACHE_DURATION
  })

  const isPartnersCacheValid = computed(() => {
    return Date.now() - partners.value.lastFetched < CACHE_DURATION
  })

  // Actions
  const fetchRoles = async (forceRefresh = false): Promise<Role[]> => {
    if (!forceRefresh && isRolesCacheValid.value && roles.value.data.length > 0) {
      return roles.value.data
    }

    roles.value.loading = true
    try {
      const response = await systemApi.getRoles({ limit: 1000 })
      if (response.status === 200) {
        const rolesData = response.message?.data || []
        roles.value.data = rolesData.map((role: any) => ({
          id: role.id,
          role_id: role.role_id || parseInt(role.id),
          name: role.name,
          role_name: role.role_name || role.name,
          description: role.description || '',
          permissions_acl: role.permissions_acl || '',
          status: role.status || '1'
        }))
        roles.value.lastFetched = Date.now()
      }
    } catch (error) {
      console.error('Error fetching roles:', error)
    } finally {
      roles.value.loading = false
    }

    return roles.value.data
  }

  const fetchPermissions = async (forceRefresh = false): Promise<Permission[]> => {
    if (!forceRefresh && isPermissionsCacheValid.value && permissions.value.data.length > 0) {
      return permissions.value.data
    }

    permissions.value.loading = true
    try {
      const response = await systemApi.getPermissions({ limit: 1000 })
      if (response.status === 200) {
        permissions.value.data = response.message?.data || []
        permissions.value.lastFetched = Date.now()
      }
    } catch (error) {
      console.error('Error fetching permissions:', error)
    } finally {
      permissions.value.loading = false
    }

    return permissions.value.data
  }

  const fetchUsers = async (forceRefresh = false): Promise<User[]> => {
    if (!forceRefresh && isUsersCacheValid.value && users.value.data.length > 0) {
      return users.value.data
    }

    users.value.loading = true
    try {
      const response = await systemApi.getUsers({ limit: 1000 })
      if (response.status === 200) {
        users.value.data = response.message?.data || []
        users.value.lastFetched = Date.now()
      }
    } catch (error) {
      console.error('Error fetching users:', error)
    } finally {
      users.value.loading = false
    }

    return users.value.data
  }

  const fetchPartners = async (forceRefresh = false): Promise<Partner[]> => {
    if (!forceRefresh && isPartnersCacheValid.value && partners.value.data.length > 0) {
      return partners.value.data
    }

    partners.value.loading = true
    try {
      const response = await partnerApi.getPartners({ limit: 1000 })
      if (response.status === 200) {
        partners.value.data = response.message?.data || []
        partners.value.lastFetched = Date.now()
      }
    } catch (error) {
      console.error('Error fetching partners:', error)
    } finally {
      partners.value.loading = false
    }

    return partners.value.data
  }

  // Utility methods
  const getRoleById = (roleId: number | string): Role | null => {
    const id = typeof roleId === 'string' ? parseInt(roleId) : roleId
    return roles.value.data.find(role => 
      role.role_id === id || parseInt(role.id) === id
    ) || null
  }

  const getPermissionById = (permissionId: number): Permission | null => {
    return permissions.value.data.find(permission => permission.id === permissionId) || null
  }

  const getUserById = (userId: string): User | null => {
    return users.value.data.find(user => user.user_id === userId) || null
  }

  const getPartnerById = (partnerId: number): Partner | null => {
    return partners.value.data.find(partner => partner.id === partnerId) || null
  }

  // Parse permissions ACL and get permission objects
  const getPermissionsFromAcl = (permissionsAcl: string): Permission[] => {
    if (!permissionsAcl) return []
    
    const separator = permissionsAcl.includes(':') ? ':' : ','
    const permissionIds = permissionsAcl
      .split(separator)
      .map(id => parseInt(id.trim()))
      .filter(id => !isNaN(id))

    return permissionIds
      .map(id => getPermissionById(id))
      .filter(permission => permission !== null) as Permission[]
  }

  // Clear cache methods
  const clearRolesCache = () => {
    roles.value.data = []
    roles.value.lastFetched = 0
  }

  const clearPermissionsCache = () => {
    permissions.value.data = []
    permissions.value.lastFetched = 0
  }

  const clearUsersCache = () => {
    users.value.data = []
    users.value.lastFetched = 0
  }

  const clearPartnersCache = () => {
    partners.value.data = []
    partners.value.lastFetched = 0
  }

  const clearAllCache = () => {
    clearRolesCache()
    clearPermissionsCache()
    clearUsersCache()
    clearPartnersCache()
  }

  // Refresh all data (for browser refresh/pull to refresh)
  const refreshAllData = async () => {
    await Promise.all([
      fetchRoles(true),
      fetchPermissions(true),
      fetchUsers(true),
      fetchPartners(true)
    ])
  }

  return {
    // State
    roles: computed(() => roles.value),
    permissions: computed(() => permissions.value),
    users: computed(() => users.value),
    partners: computed(() => partners.value),

    // Cache validity
    isRolesCacheValid,
    isPermissionsCacheValid,
    isUsersCacheValid,
    isPartnersCacheValid,

    // Actions
    fetchRoles,
    fetchPermissions,
    fetchUsers,
    fetchPartners,

    // Utility methods
    getRoleById,
    getPermissionById,
    getUserById,
    getPartnerById,
    getPermissionsFromAcl,

    // Cache management
    clearRolesCache,
    clearPermissionsCache,
    clearUsersCache,
    clearPartnersCache,
    clearAllCache,
    refreshAllData
  }
})
