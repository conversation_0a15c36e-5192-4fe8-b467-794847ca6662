import { apiClient } from './apiClient'
import { createHash<PERSON><PERSON> } from '@/utils/hash'
import type { ApiResponse, PaginationParams, PaginatedResponse } from './types'

/**
 * Get current timestamp for API requests
 */
const getCurrentTimestamp = (): number => {
  return Math.floor(Date.now() / 1000)
}

/**
 * Get authentication headers for API requests
 */
const getAuthHeaders = () => {
  const token = localStorage.getItem('token')
  const selectedClientId = localStorage.getItem('selectedClientId')
  const selectedPartnerId = localStorage.getItem('selectedPartnerId')

  const headers: Record<string, string> = {}

  if (token) {
    headers['X-Access'] = token
  }

  if (selectedClientId) {
    headers['X-Client-ID'] = selectedClientId
  }

  if (selectedPartnerId) {
    headers['X-Partner-ID'] = selectedPartnerId
  }

  return headers
}

export interface Partner {
  id: number
  name: string
  description?: string
  status: number | string
  address?: string
  country?: string
  msisdn?: string
  created_at: string
  updated_at?: string
  balance?: number
  services?: PartnerService[]
}

export interface Service {
  id: number
  name: string
}

export interface PartnerService {
  id: number
  partner_id: number
  service_id: number
  rate_limit_per_minute: number
  status: 'active' | 'inactive'
  service_name?: string // Populated from join with services table
  created_at?: string
}

export interface PartnerBet {
  id: number
  partner_id: number
  user_id: number
  bet_amount: number
  potential_win: number
  status: string
  created_at: string
}

export interface PartnerBalance {
  partner_id: number
  current_balance: number
  available_balance: number
  pending_balance: number
  last_updated: string
}

export interface PartnerSettings {
  id: number
  partner_id: number
  api_key: string
  ip_address?: string
  callback_url?: string
  currency: string
  billing_mode: 'prepay' | 'postpay'
  rate_limit: number
  timezone: string
  version: string
  websites?: string[]
  status: number
  created_at: string
  updated_at?: string
}

export interface PartnerTransaction {
  id: number
  partner_id: number
  transaction_type: string
  amount: number
  status: string
  description?: string
  created_at: string
}

export interface PartnerSettings {
  id: number
  partner_id: number
  api_key: string
  ip_address?: string
  callback_url?: string
  currency?: string
  denomination?: string
  timezone?: string
  billing_mode: 'prepay' | 'postpay'
  rate_limit?: number
  websites?: string[]
  version: number
  created_at: string
}

export interface PartnerBalanceSummary {
  partner_id: number
  current_balance: number
  bonus_balance: number
  total_deposits: number
  total_withdrawals: number
  total_bets: number
  total_winnings: number
  last_transaction_date: string
}

export const partnerApi = {
  /**
   * Get partners list
   */
  async getPartners(params: PaginationParams & {
    partner_id?: string
    status?: string | number
  } = {}): Promise<ApiResponse<PaginatedResponse<Partner>>> {
    try {
      const payload = {
        page: params.page || 1,
        limit: params.limit || 10,
        search: params.search || '',
        partner_id: params.partner_id || '',
        status: params.status || '',
        start: params.start || '',
        end: params.end || '',
        export: params.export || false,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('partners/v1/view', payload, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })

      const responseData = response.data.data

      // Map the API response to match expected interface
      const partners = responseData.data?.data || responseData.data.result
      const mappedPartners = partners.map((partner: any) => ({
        id: parseInt(partner.partner_id || partner.id),
        name: partner.partner_name || partner.name,
        description: partner.partner_desc || partner.description,
        status: parseInt(partner.status),
        created_at: partner.created_at || new Date().toISOString(),
        updated_at: partner.updated_at || new Date().toISOString(),
        balance: parseFloat(partner.balance || 0)
      }))

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: mappedPartners,
          total: parseInt(responseData.data?.total) || 0,
          current_page: params.page || 1,
          limit: payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get partners error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {
            data: [],
            total: 0,
            current_page: 1,
            limit: params.limit || 10
          },
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {
          data: [],
          total: 0,
          current_page: 1,
          limit: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Create new partner
   */
  async createPartner(partnerData: {
    name: string
    description?: string
    status?: number
  }): Promise<ApiResponse<Partner>> {
    try {
      const payload = {
        partner_name: partnerData.name,
        partner_desc: partnerData.description || '',
        status: partnerData.status || 1,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('partners/v1/create', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Create partner error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {},
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Update partner
   */
  async updatePartner(partnerId: number, partnerData: {
    name?: string
    description?: string
    status?: number
  }): Promise<ApiResponse<Partner>> {
    try {
      const payload = {
        partner_name: partnerData.name,
        partner_desc: partnerData.description,
        status: partnerData.status,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post(`partners/v1/update/${partnerId}`, payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Update partner error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: {},
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Delete partner
   */
  async deletePartner(partnerId: number): Promise<ApiResponse<any>> {
    try {
      const payload = {
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post(`partners/v1/delete/${partnerId}`, payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.message || 'Partner deleted successfully',
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Delete partner error:', error)
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        return {
          status: data.data?.code || status,
          message: data.data?.message || 'Failed to delete partner',
          code: (data.data?.code || status).toString()
        }
      }
      
      return {
        status: 500,
        message: 'Failed to delete partner',
        code: '500'
      }
    }
  },

  /**
   * Get partner bets
   */
  async getPartnerBets(params: PaginationParams & {
    partner_id?: string
  } = {}): Promise<ApiResponse<PaginatedResponse<PartnerBet>>> {
    try {
      const payload = {
        page: params.page || 1,
        limit: params.limit || 10,
        partner_id: params.partner_id || '',
        start: params.start || '',
        end: params.end || '',
        status: params.status || '',
        export: params.export || false,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('partners/v1/partners_bets', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data?.data || [],
          total: parseInt(responseData.data?.total) || 0,
          current_page: params.page || 1,
          limit: payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get partner bets error:', error)
      
      return {
        status: 500,
        message: {
          data: [],
          total: 0,
          current_page: 1,
          limit: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Get partner balance
   */
  async getPartnerBalance(partnerId: string): Promise<ApiResponse<PartnerBalance>> {
    try {
      const payload = {
        partner_id: partnerId,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('partners/v1/partner_balance', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get partner balance error:', error)

      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Get partner balance transactions
   */
  async getPartnerBalanceTransactions(params: PaginationParams & {
    partner_id?: string
  } = {}): Promise<ApiResponse<PaginatedResponse<PartnerTransaction>>> {
    try {
      const payload = {
        page: params.page || 1,
        limit: params.limit || 10,
        partner_id: params.partner_id || '',
        start: params.start || '',
        end: params.end || '',
        status: params.status || '',
        export: params.export || false,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('partners/v1/partner_balance_transactions', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data?.data || [],
          total: parseInt(responseData.data?.total) || 0,
          current_page: params.page || 1,
          limit: payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get partner balance transactions error:', error)

      return {
        status: 500,
        message: {
          data: [],
          total: 0,
          current_page: 1,
          limit: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Get partner balance summary
   */
  async getPartnerBalanceSummary(partnerId: string): Promise<ApiResponse<PartnerBalanceSummary>> {
    try {
      const payload = {
        partner_id: partnerId,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('partners/v1/partner_balance_summary', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get partner balance summary error:', error)

      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Get partner services
   */
  async getPartnerServices(params: PaginationParams & {
    partner_id?: string
  } = {}): Promise<ApiResponse<PaginatedResponse<PartnerService>>> {
    try {
      const payload = {
        page: params.page || 1,
        limit: params.limit || 10,
        partner_id: params.partner_id || '',
        start: params.start || '',
        end: params.end || '',
        status: params.status || '',
        export: params.export || false,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('services/v1/partner_services', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: {
          data: responseData.data?.data || [],
          total: parseInt(responseData.data?.total) || 0,
          current_page: params.page || 1,
          limit: payload.limit
        },
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get partner services error:', error)

      return {
        status: 500,
        message: {
          data: [],
          total: 0,
          current_page: 1,
          limit: params.limit || 10
        },
        code: '500'
      }
    }
  },

  /**
   * Create partner service
   */
  async createPartnerService(serviceData: {
    partner_id: number
    service_id: number
    rate_limit_per_minute?: number
    status?: string
  }): Promise<ApiResponse<PartnerService>> {
    try {
      const payload = {
        partner_id: serviceData.partner_id,
        service_id: serviceData.service_id,
        rate_limit_per_minute: serviceData.rate_limit_per_minute || 60,
        status: serviceData.status || 'active',
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('partners/v1/service/create', payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Create partner service error:', error)

      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Update partner service
   */
  async updatePartnerService(serviceId: number, serviceData: {
    rate_limit_per_minute?: number
    status?: string
  }): Promise<ApiResponse<PartnerService>> {
    try {
      const payload = {
        rate_limit_per_minute: serviceData.rate_limit_per_minute,
        status: serviceData.status,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post(`partners/v1/service/update/${serviceId}`, payload, {
        headers: {
          'X-Hash-Key': hash
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Update partner service error:', error)

      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Get partner settings
   */
  async getPartnerSettings(partnerId: string): Promise<ApiResponse<PartnerSettings>> {
    try {
      const payload = {
        partner_id: partnerId,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('partners/v1/partner_settings', payload, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get partner settings error:', error)

      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Create partner settings
   */
  async createPartnerSettings(settingsData: {
    partner_id: number
    api_key: string
    ip_address?: string
    callback_url?: string
    currency?: string
    denomination?: string
    timezone?: string
    billing_mode?: 'prepay' | 'postpay'
    rate_limit?: number
    websites?: string[]
  }): Promise<ApiResponse<PartnerSettings>> {
    try {
      const payload = {
        partner_id: settingsData.partner_id,
        api_key: settingsData.api_key,
        ip_address: settingsData.ip_address || '',
        callback_url: settingsData.callback_url || '',
        currency: settingsData.currency || 'KES',
        denomination: settingsData.denomination || '',
        timezone: settingsData.timezone || 'Africa/Nairobi',
        billing_mode: settingsData.billing_mode || 'prepay',
        rate_limit: settingsData.rate_limit || 60,
        websites: settingsData.websites || [],
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('partners/v1/partner_settings/create', payload, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Create partner settings error:', error)

      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Update partner settings
   */
  async updatePartnerSettings(settingsId: number, settingsData: {
    api_key?: string
    ip_address?: string
    callback_url?: string
    currency?: string
    denomination?: string
    timezone?: string
    billing_mode?: 'prepay' | 'postpay'
    rate_limit?: number
    websites?: string[]
  }): Promise<ApiResponse<PartnerSettings>> {
    try {
      const payload = {
        api_key: settingsData.api_key,
        ip_address: settingsData.ip_address,
        callback_url: settingsData.callback_url,
        currency: settingsData.currency,
        denomination: settingsData.denomination,
        timezone: settingsData.timezone,
        billing_mode: settingsData.billing_mode,
        rate_limit: settingsData.rate_limit,
        websites: settingsData.websites,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post(`partners/v1/partner_settings/update/${settingsId}`, payload, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Update partner settings error:', error)

      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Get partner services
   */
  async getPartnerServices(partnerId: number): Promise<ApiResponse<any[]>> {
    try {
      const payload = {
        partner_id: partnerId,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('partners/v1/partner_services/list', payload, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || [],
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Get partner services error:', error)

      return {
        status: 500,
        message: [],
        code: '500'
      }
    }
  },

  /**
   * Create partner service
   */
  async createPartnerService(serviceData: {
    partner_id: number
    service_id: number
    rate_limit_per_minute?: number
    status?: 'active' | 'inactive'
  }): Promise<ApiResponse<any>> {
    try {
      const payload = {
        partner_id: serviceData.partner_id,
        service_id: serviceData.service_id,
        rate_limit_per_minute: serviceData.rate_limit_per_minute || 60,
        status: serviceData.status || 'active',
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post('partners/v1/partner_services/create', payload, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Create partner service error:', error)

      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Update partner service
   */
  async updatePartnerService(partnerServiceId: number, serviceData: {
    rate_limit_per_minute?: number
    status?: 'active' | 'inactive'
  }): Promise<ApiResponse<any>> {
    try {
      const payload = {
        rate_limit_per_minute: serviceData.rate_limit_per_minute,
        status: serviceData.status,
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post(`partners/v1/partner_services/update/${partnerServiceId}`, payload, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.data || {},
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Update partner service error:', error)

      return {
        status: 500,
        message: {},
        code: '500'
      }
    }
  },

  /**
   * Delete partner service
   */
  async deletePartnerService(partnerServiceId: number): Promise<ApiResponse<any>> {
    try {
      const payload = {
        timestamp: getCurrentTimestamp()
      }

      const hash = createHashKey(payload)

      const response = await apiClient.post(`partners/v1/partner_services/delete/${partnerServiceId}`, payload, {
        headers: {
          'X-Hash-Key': hash,
          ...getAuthHeaders()
        }
      })

      const responseData = response.data.data

      return {
        status: responseData.code === 200 ? 200 : responseData.code,
        message: responseData.message || 'Partner service deleted successfully',
        code: responseData.code.toString()
      }
    } catch (error: any) {
      console.error('Delete partner service error:', error)

      return {
        status: 500,
        message: 'Failed to delete partner service',
        code: '500'
      }
    }
  }
}
