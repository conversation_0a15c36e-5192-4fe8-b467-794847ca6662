/**
 * CSV Export Utilities
 * Handles local CSV export functionality
 */

export interface ExportColumn {
  key: string
  label: string
  format?: (value: any) => string
}

/**
 * Convert array of objects to CSV string
 */
export function arrayToCSV(data: any[], columns: ExportColumn[]): string {
  if (!data || data.length === 0) {
    return ''
  }

  // Create header row
  const headers = columns.map(col => `"${col.label}"`).join(',')
  
  // Create data rows
  const rows = data.map(item => {
    return columns.map(col => {
      let value = item[col.key]
      
      // Apply custom formatting if provided
      if (col.format && typeof col.format === 'function') {
        value = col.format(value)
      }
      
      // Handle null/undefined values
      if (value === null || value === undefined) {
        value = ''
      }
      
      // Convert to string and escape quotes
      value = String(value).replace(/"/g, '""')
      
      return `"${value}"`
    }).join(',')
  })
  
  return [headers, ...rows].join('\n')
}

/**
 * Download CSV file
 */
export function downloadCSV(csvContent: string, filename: string): void {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}

/**
 * Export partners data to CSV
 */
export function exportPartnersCSV(partners: any[], filename?: string): void {
  const columns: ExportColumn[] = [
    { key: 'id', label: 'ID' },
    { key: 'name', label: 'Partner Name' },
    { key: 'country', label: 'Country' },
    { key: 'address', label: 'Address' },
    { key: 'msisdn', label: 'Phone Number' },
    { key: 'email_address', label: 'Email' },
    { 
      key: 'status', 
      label: 'Status',
      format: (value) => value === '1' || value === 1 ? 'Active' : 'Inactive'
    },
    { 
      key: 'created_at', 
      label: 'Created Date',
      format: (value) => {
        if (!value) return ''
        try {
          return new Date(value).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })
        } catch {
          return value
        }
      }
    }
  ]
  
  const csvContent = arrayToCSV(partners, columns)
  const defaultFilename = `partners_export_${new Date().toISOString().split('T')[0]}.csv`
  
  downloadCSV(csvContent, filename || defaultFilename)
}

/**
 * Export partner bets data to CSV
 */
export function exportPartnerBetsCSV(bets: any[], filename?: string): void {
  const columns: ExportColumn[] = [
    { key: 'id', label: 'Bet ID' },
    { key: 'partner_name', label: 'Partner' },
    { key: 'user_id', label: 'User ID' },
    { key: 'amount', label: 'Amount' },
    { key: 'currency', label: 'Currency' },
    { key: 'status', label: 'Status' },
    { key: 'game_type', label: 'Game Type' },
    { 
      key: 'created_at', 
      label: 'Bet Date',
      format: (value) => {
        if (!value) return ''
        try {
          return new Date(value).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          }) + ' UTC'
        } catch {
          return value
        }
      }
    },
    { 
      key: 'settled_at', 
      label: 'Settled Date',
      format: (value) => {
        if (!value) return 'Not Settled'
        try {
          return new Date(value).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          }) + ' UTC'
        } catch {
          return value
        }
      }
    }
  ]
  
  const csvContent = arrayToCSV(bets, columns)
  const defaultFilename = `partner_bets_export_${new Date().toISOString().split('T')[0]}.csv`
  
  downloadCSV(csvContent, filename || defaultFilename)
}

/**
 * Export users data to CSV
 */
export function exportUsersCSV(users: any[], filename?: string): void {
  const columns: ExportColumn[] = [
    { key: 'user_id', label: 'User ID' },
    { key: 'display_name', label: 'Display Name' },
    { key: 'username', label: 'Username' },
    { key: 'email_address', label: 'Email' },
    { key: 'msisdn', label: 'Phone' },
    { key: 'role_name', label: 'Role' },
    { key: 'user_type', label: 'Type' },
    { 
      key: 'status', 
      label: 'Status',
      format: (value) => value === 1 || value === '1' ? 'Active' : 'Inactive'
    },
    { 
      key: 'created_at', 
      label: 'Created Date',
      format: (value) => {
        if (!value) return ''
        try {
          return new Date(value).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })
        } catch {
          return value
        }
      }
    },
    { 
      key: 'last_login', 
      label: 'Last Login',
      format: (value) => {
        if (!value) return 'Never'
        try {
          return new Date(value).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })
        } catch {
          return value
        }
      }
    }
  ]
  
  const csvContent = arrayToCSV(users, columns)
  const defaultFilename = `users_export_${new Date().toISOString().split('T')[0]}.csv`
  
  downloadCSV(csvContent, filename || defaultFilename)
}

/**
 * Format currency values with 2 decimal places
 */
export function formatCurrency(value: any): string {
  if (value === null || value === undefined || value === '') {
    return '0.00'
  }
  
  const num = parseFloat(String(value))
  if (isNaN(num)) {
    return '0.00'
  }
  
  return num.toFixed(2)
}

/**
 * Format date in dd/mm/yyyy hh:mm UTC format
 */
export function formatDateTime(value: any): string {
  if (!value) return ''
  
  try {
    const date = new Date(value)
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }) + ' UTC'
  } catch {
    return String(value)
  }
}
