/**
 * Authentication testing utilities
 * Helps verify that API calls are properly authenticated
 */

import { systemApi } from '@/services/systemApi'
import { partnerApi } from '@/services/partnerApi'
import { servicesApi } from '@/services/servicesApi'
import { getAuthContext, validateAuthHeaders } from '@/utils/auth'

/**
 * Test authentication for all API services
 */
export const testAuthentication = async () => {
  console.group('🔐 Authentication Test')
  
  // Check authentication context
  const authContext = getAuthContext()
  console.log('Auth Context:', authContext)
  
  // Validate headers
  const validation = validateAuthHeaders()
  console.log('Header Validation:', validation)
  
  if (!validation.valid) {
    console.error('❌ Authentication validation failed:', validation.missing)
    console.groupEnd()
    return false
  }
  
  // Test API calls
  const testResults = {
    systemApi: await testSystemApi(),
    partnerApi: await testPartnerApi(),
    servicesApi: await testServicesApi()
  }
  
  console.log('Test Results:', testResults)
  console.groupEnd()
  
  return testResults
}

/**
 * Test System API authentication
 */
const testSystemApi = async () => {
  try {
    console.log('Testing System API...')
    
    // Test users endpoint
    const usersResponse = await systemApi.getUsers({ limit: 1 })
    console.log('✅ Users API:', usersResponse.status === 200 ? 'Success' : 'Failed')
    
    // Test roles endpoint
    const rolesResponse = await systemApi.getRoles({ limit: 1 })
    console.log('✅ Roles API:', rolesResponse.status === 200 ? 'Success' : 'Failed')
    
    return {
      users: usersResponse.status === 200,
      roles: rolesResponse.status === 200,
      overall: usersResponse.status === 200 && rolesResponse.status === 200
    }
  } catch (error: any) {
    console.error('❌ System API test failed:', error.message)
    return {
      users: false,
      roles: false,
      overall: false,
      error: error.message
    }
  }
}

/**
 * Test Partner API authentication
 */
const testPartnerApi = async () => {
  try {
    console.log('Testing Partner API...')
    
    // Test partners endpoint
    const partnersResponse = await partnerApi.getPartners({ limit: 1 })
    console.log('✅ Partners API:', partnersResponse.status === 200 ? 'Success' : 'Failed')
    
    // Test partner settings endpoint
    const settingsResponse = await partnerApi.getPartnerSettings({ limit: 1 })
    console.log('✅ Partner Settings API:', settingsResponse.status === 200 ? 'Success' : 'Failed')
    
    return {
      partners: partnersResponse.status === 200,
      settings: settingsResponse.status === 200,
      overall: partnersResponse.status === 200 && settingsResponse.status === 200
    }
  } catch (error: any) {
    console.error('❌ Partner API test failed:', error.message)
    return {
      partners: false,
      settings: false,
      overall: false,
      error: error.message
    }
  }
}

/**
 * Test Services API authentication
 */
const testServicesApi = async () => {
  try {
    console.log('Testing Services API...')
    
    // Test services endpoint
    const servicesResponse = await servicesApi.getServices({ limit: 1 })
    console.log('✅ Services API:', servicesResponse.status === 200 ? 'Success' : 'Failed')
    
    return {
      services: servicesResponse.status === 200,
      overall: servicesResponse.status === 200
    }
  } catch (error: any) {
    console.error('❌ Services API test failed:', error.message)
    return {
      services: false,
      overall: false,
      error: error.message
    }
  }
}

/**
 * Debug authentication headers for a specific request
 */
export const debugAuthHeaders = () => {
  const authContext = getAuthContext()
  
  console.group('🔍 Authentication Debug')
  console.log('Token:', authContext.token ? '✅ Present' : '❌ Missing')
  console.log('Client ID:', authContext.clientId ? '✅ Present' : '❌ Missing')
  console.log('Partner ID:', authContext.partnerId ? '✅ Present' : '❌ Missing')
  console.log('Is Authenticated:', authContext.isAuthenticated ? '✅ Yes' : '❌ No')
  
  // Show actual values (be careful in production)
  if (import.meta.env.DEV) {
    console.log('Token Value:', authContext.token)
    console.log('Client ID Value:', authContext.clientId)
    console.log('Partner ID Value:', authContext.partnerId)
  }
  
  console.groupEnd()
}

/**
 * Simulate authentication error scenarios
 */
export const testAuthErrorScenarios = async () => {
  console.group('🚨 Authentication Error Testing')
  
  // Save current auth state
  const originalToken = localStorage.getItem('token')
  const originalClientId = localStorage.getItem('selectedClientId')
  const originalPartnerId = localStorage.getItem('selectedPartnerId')
  
  try {
    // Test with no token
    console.log('Testing with no token...')
    localStorage.removeItem('token')
    
    try {
      await systemApi.getUsers({ limit: 1 })
      console.log('❌ No token test: Should have failed but succeeded')
    } catch (error: any) {
      console.log('✅ No token test: Correctly failed with:', error.message)
    }
    
    // Test with invalid token
    console.log('Testing with invalid token...')
    localStorage.setItem('token', 'invalid-token-12345')
    
    try {
      await systemApi.getUsers({ limit: 1 })
      console.log('❌ Invalid token test: Should have failed but succeeded')
    } catch (error: any) {
      console.log('✅ Invalid token test: Correctly failed with:', error.message)
    }
    
  } finally {
    // Restore original auth state
    if (originalToken) localStorage.setItem('token', originalToken)
    if (originalClientId) localStorage.setItem('selectedClientId', originalClientId)
    if (originalPartnerId) localStorage.setItem('selectedPartnerId', originalPartnerId)
  }
  
  console.groupEnd()
}

/**
 * Monitor authentication status
 */
export const monitorAuthStatus = () => {
  const checkAuth = () => {
    const authContext = getAuthContext()
    console.log('🔐 Auth Status:', {
      timestamp: new Date().toISOString(),
      authenticated: authContext.isAuthenticated,
      hasToken: !!authContext.token,
      hasClientId: !!authContext.clientId,
      hasPartnerId: !!authContext.partnerId
    })
  }
  
  // Check immediately
  checkAuth()
  
  // Check every 30 seconds
  const interval = setInterval(checkAuth, 30000)
  
  // Return cleanup function
  return () => clearInterval(interval)
}

/**
 * Validate specific API endpoint authentication
 */
export const validateEndpointAuth = async (endpoint: string, method: string = 'GET') => {
  console.group(`🔍 Validating ${method} ${endpoint}`)
  
  const authContext = getAuthContext()
  console.log('Auth Context:', authContext)
  
  if (!authContext.isAuthenticated) {
    console.error('❌ Not authenticated')
    console.groupEnd()
    return false
  }
  
  try {
    // This would need to be implemented based on your specific API structure
    console.log('✅ Endpoint validation would go here')
    console.groupEnd()
    return true
  } catch (error: any) {
    console.error('❌ Endpoint validation failed:', error.message)
    console.groupEnd()
    return false
  }
}

/**
 * Export for development console access
 */
if (import.meta.env.DEV) {
  // Make functions available in development console
  ;(window as any).authTest = {
    testAuthentication,
    debugAuthHeaders,
    testAuthErrorScenarios,
    monitorAuthStatus,
    validateEndpointAuth
  }
  
  console.log('🔧 Auth testing utilities available at window.authTest')
}
