# User Management System Tests

This directory contains comprehensive tests for the enhanced user management system with role-based access control and partner assignment functionality.

## Test Coverage

### 1. Role Template System
- ✅ Role template fetching and parsing
- ✅ Permissions ACL parsing (comma and colon separated)
- ✅ Admin vs Partner role identification
- ✅ User type determination from role

### 2. Component Testing
- ✅ RoleTemplateSelector component functionality
- ✅ PartnerAssignment component for admin and partner users
- ✅ PermissionManager component with role and individual modes
- ✅ Form validation and error handling

### 3. API Integration
- ✅ User creation with enhanced fields
- ✅ User updating with enhanced fields
- ✅ Partner assignment functionality
- ✅ Permission management
- ✅ Data validation according to database schema

### 4. Authentication Store
- ✅ Enhanced user data storage
- ✅ Role name decoding and display
- ✅ Partner information handling

### 5. Data Validation
- ✅ Database schema compliance
- ✅ Field length validation
- ✅ Email and phone number format validation
- ✅ Required field validation

## Running Tests

### Prerequisites
```bash
npm install vitest @vue/test-utils
```

### Run All Tests
```bash
npm run test
```

### Run User Management Tests Only
```bash
npm run test tests/user-management.test.js
```

### Run Tests with Coverage
```bash
npm run test:coverage
```

## Test Scenarios

### Admin User Scenarios
1. **Create Admin User**
   - Select admin role template
   - Assign multiple partners
   - Verify permissions are auto-populated
   - Validate form submission

2. **Edit Admin User**
   - Change role template
   - Modify partner assignments
   - Update permissions
   - Verify data persistence

### Partner User Scenarios
1. **Create Partner User**
   - Select partner role template
   - Restrict to assigned partners only
   - Verify limited permissions
   - Validate form submission

2. **Edit Partner User**
   - Maintain partner restrictions
   - Update within allowed permissions
   - Verify data persistence

### Role Template Scenarios
1. **Role Selection**
   - Load available role templates
   - Parse permissions ACL correctly
   - Auto-populate permissions based on role
   - Handle both comma and colon separated formats

2. **Permission Management**
   - Individual permission selection
   - Bulk permission assignment via roles
   - Permission search and filtering
   - Module-based organization

### Data Validation Scenarios
1. **Valid Data**
   - All required fields present
   - Correct data types and formats
   - Within database field limits
   - Valid foreign key references

2. **Invalid Data**
   - Missing required fields
   - Invalid email/phone formats
   - Exceeding field length limits
   - Invalid role/partner references

## Manual Testing Checklist

### User Creation Flow
- [ ] Navigate to Add User page
- [ ] Fill in basic information (name, email, phone)
- [ ] Select role template
- [ ] Verify permissions auto-populate
- [ ] Assign partners (admin) or verify restrictions (partner)
- [ ] Submit form and verify success
- [ ] Check user appears in user list with correct data

### User Editing Flow
- [ ] Navigate to user list
- [ ] Click edit on existing user
- [ ] Verify form pre-populated with current data
- [ ] Modify role template
- [ ] Verify permissions update automatically
- [ ] Change partner assignments
- [ ] Submit form and verify changes saved
- [ ] Check user list reflects updates

### Role-Based Access Control
- [ ] Login as admin user
- [ ] Verify access to all system features
- [ ] Can create/edit users with any role
- [ ] Can assign any partners
- [ ] Login as partner user
- [ ] Verify restricted access
- [ ] Can only see assigned partners
- [ ] Limited permission set

### Data Persistence
- [ ] Create user with specific permissions
- [ ] Logout and login again
- [ ] Verify user data persisted correctly
- [ ] Edit user and save changes
- [ ] Verify changes persisted
- [ ] Check database records match UI data

## Expected API Endpoints

The tests assume the following API endpoints exist:

### User Management
- `POST /users/v1/account_create` - Create new user
- `POST /system/v1/users/edit/{id}` - Update existing user
- `GET /system/v1/users` - List users with filters
- `POST /system/v1/users/assign-partners` - Assign partners to user
- `POST /system/v1/users/partners` - Get user's assigned partners

### Role Templates
- `POST /system/v1/roles/templates` - Get role templates
- `GET /system/v1/roles` - List all roles
- `POST /system/v1/role/create` - Create new role

### Permissions
- `GET /system/v1/permissions` - List all permissions
- `POST /system/v1/permission/create` - Create new permission

### Partners
- `POST /partners/v1/view` - List partners

## Database Schema Validation

Tests verify compliance with the provided database schema:

### user table
- `id` (auto_increment)
- `msisdn` (bigint)
- `user_name` (varchar 65)
- `display_name` (varchar 200)
- `password` (varchar 700)
- `status` (int, default 1)
- `role_id` (int)
- `type` (enum: Admin/Partner, default Partner)
- `created_at` (datetime)
- `updated_at` (timestamp)
- `created_by` (int)

### user_permissions table
- `id` (auto_increment)
- `name` (varchar 65, unique)
- `description` (varchar 150)
- `status` (int, default 1)
- `created_at` (datetime)
- `updated_at` (timestamp)
- `deleted` (datetime)
- `user_id` (int)

### user_roles table
- `id` (auto_increment)
- `name` (varchar 30, unique)
- `description` (varchar 100)
- `permissions_acl` (varchar 500)
- `status` (int)
- `created_by` (int)
- `updated_by` (int)

### partners table
- `id` (auto_increment)
- `name` (varchar 255)
- `user_id` (int)
- `email_address` (varchar 255)
- `address` (varchar 255)
- `country` (varchar 100)
- `dial_code` (varchar 10)
- `msisdn` (varchar 20)
- `status` (tinyint, default 1)
- `created_by` (int)
- `created_at` (timestamp)
- `updated_at` (timestamp)

## Troubleshooting

### Common Issues
1. **API Endpoint Not Found**: Verify backend implements expected endpoints
2. **Permission Denied**: Check user has required permissions for action
3. **Validation Errors**: Verify data meets database schema requirements
4. **Partner Assignment Fails**: Check partner IDs exist and are valid

### Debug Tips
1. Check browser console for API errors
2. Verify network requests in browser dev tools
3. Check backend logs for validation errors
4. Verify database constraints are met
